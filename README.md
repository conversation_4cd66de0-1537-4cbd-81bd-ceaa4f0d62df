# المحاسب الشامل

نظام محاسبي شامل مبني باستخدام Django لإدارة الحسابات المالية والرواتب وشؤون الموظفين.

## المميزات الرئيسية

- ✨ واجهة مستخدم عربية سهلة الاستخدام
- 💼 إدارة شؤون الموظفين
- 💰 إدارة الرواتب والمخصصات والاستقطاعات
- 🏦 إدارة الحسابات المالية والمعاملات
- 📊 تقارير متنوعة
- 🔒 نظام صلاحيات متكامل
- 📦 نسخ احتياطية تلقائية

## متطلبات النظام

- Python 3.8 أو أحدث
- PostgreSQL 12 أو أحدث
- تثبيت المكتبات المطلوبة من ملف requirements.txt

## التثبيت

1. إنشاء بيئة Python افتراضية:
```bash
python -m venv venv
```

2. تفعيل البيئة الافتراضية:
- Windows:
```bash
venv\Scripts\activate
```
- Linux/Mac:
```bash
source venv/bin/activate
```

3. تثبيت المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

4. إنشاء ملف .env وتعديل الإعدادات المطلوبة:
```env
DEBUG=True
SECRET_KEY=your-secret-key
DB_NAME=accounting_db
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432
```

5. تهيئة قاعدة البيانات:
```bash
python manage.py migrate
```

6. تهيئة النظام:
```bash
python manage.py initialize_system
```

7. تشغيل الخادم المحلي:
```bash
python manage.py runserver
```

## الاستخدام

1. الدخول إلى النظام:
- اسم المستخدم: admin
- كلمة المرور: admin123

2. تغيير كلمة المرور الافتراضية من خلال الملف الشخصي.

3. إكمال بيانات المؤسسة من خلال الإعدادات.

## إدارة النسخ الاحتياطية

- إنشاء نسخة احتياطية:
```bash
python manage.py manage_backups create
```

- استعادة نسخة احتياطية:
```bash
python manage.py manage_backups restore --file=backup_file.zip
```

- عرض النسخ الاحتياطية المتوفرة:
```bash
python manage.py manage_backups list
```

- تنظيف النسخ الاحتياطية القديمة:
```bash
python manage.py manage_backups clean --days=30
```

## إنشاء التقارير

- تقرير اليومية العامة:
```bash
python manage.py generate_reports daily_journal --date=2025-06-06
```

- كشف حساب:
```bash
python manage.py generate_reports account_statement --account=1001 --start_date=2025-01-01 --end_date=2025-12-31
```

- تقرير الرواتب:
```bash
python manage.py generate_reports salary_report --start_date=2025-06-01 --end_date=2025-06-30
```

## الأمان

- يجب تغيير كلمة المرور الافتراضية للمشرف
- يجب تغيير مفتاح SECRET_KEY في الإنتاج
- يتم تشفير جميع كلمات المرور
- يتم تسجيل جميع النشاطات في النظام

## المساهمة

نرحب بمساهماتكم لتحسين النظام. يرجى اتباع الخطوات التالية:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة: `git checkout -b feature/amazing-feature`
3. تنفيذ التغييرات: `git commit -m 'Add amazing feature'`
4. رفع التغييرات: `git push origin feature/amazing-feature`
5. إنشاء طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE).