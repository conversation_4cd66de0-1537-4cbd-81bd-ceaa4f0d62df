# النظام المحاسبي الموحد - رواتب الموظفين

## 📋 نظرة عامة

النظام المحاسبي الموحد هو تطبيق شامل لإدارة رواتب الموظفين والعمليات المحاسبية المرتبطة بها. تم تطويره باستخدام Python و Django للواجهة الخلفية و PyQt5 لواجهة سطح المكتب.

## ✨ المميزات الرئيسية

### 👥 إدارة المستخدمين والأمان
- مجموعات المستخدمين مع صلاحيات مخصصة
- نظام تسجيل دخول آمن مع تشفير كلمات المرور
- تدقيق شامل لجميع العمليات (Audit Trails)
- إدارة الأدوار والصلاحيات

### ⚙️ الإعدادات والتهيئة
- إدارة بيانات المؤسسة والدوائر
- دعم العملات المتعددة
- إدارة الفترات المحاسبية
- نظام النسخ الاحتياطي التلقائي

### 🧑‍💼 إدارة الموظفين
- دليل شامل للأقسام والشعب
- إدارة العناوين الوظيفية والشهادات
- نظام الدرجات الوظيفية والمراحل
- استيراد وتصدير بيانات الموظفين من/إلى Excel

### 💼 النظام المحاسبي
- دليل حسابات قابل للتخصيص
- إدارة الصناديق والمصارف
- الحسابات البنكية (التشغيلية والخزينة الموحد)
- سندات الصرف والقبض
- قيود اليومية التلقائية
- مراكز التكلفة

### 💰 نظام الرواتب
- حساب الرواتب والمخصصات تلقائياً
- إدارة الاستقطاعات والضرائب
- شرائح ضريبية قابلة للتهيئة
- ربط تلقائي مع النظام المحاسبي
- مسيرات رواتب شهرية

### 📊 التقارير والتحليلات
- تقارير مالية شاملة
- تقارير الرواتب المفصلة
- لوحات معلومات تفاعلية
- تحليلات متقدمة ومؤشرات أداء
- تصدير إلى PDF و Excel

## 🛠️ التقنيات المستخدمة

### Backend
- **Python 3.8+**
- **Django 4.2.7** - إطار العمل الرئيسي
- **Django REST Framework** - API
- **PostgreSQL/SQLite** - قاعدة البيانات
- **Celery + Redis** - المهام الخلفية

### Frontend
- **PyQt5** - واجهة سطح المكتب
- **Bootstrap 5** - واجهة الويب
- **jQuery** - التفاعل
- **Chart.js** - الرسوم البيانية

### Data Processing
- **Pandas** - معالجة البيانات
- **openpyxl** - ملفات Excel
- **ReportLab** - تقارير PDF

## 📦 التثبيت والإعداد

### 1. متطلبات النظام
```bash
Python 3.8+
pip
virtualenv (اختياري)
```

### 2. تحميل المشروع
```bash
git clone https://github.com/your-repo/payroll-system.git
cd payroll-system
```

### 3. إنشاء البيئة الافتراضية
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 5. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
python manage.py createsuperuser
```

### 6. تشغيل الخادم
```bash
# خادم Django
python manage.py runserver

# تطبيق سطح المكتب
python desktop_app.py
```

## 🔧 الإعداد والتكوين

### متغيرات البيئة (.env)
```env
SECRET_KEY=your-secret-key
DEBUG=True
DB_ENGINE=django.db.backends.postgresql
DB_NAME=payroll_db
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_HOST=localhost
DB_PORT=5432
```

### إعدادات الشركة
1. سجل دخول إلى لوحة الإدارة
2. أضف بيانات المؤسسة
3. حدد العملة الافتراضية
4. أنشئ الفترة المحاسبية الحالية

## 📖 دليل الاستخدام

### إدارة الموظفين
1. **إضافة موظف جديد:**
   - انتقل إلى قسم الموظفين
   - اضغط "إضافة موظف"
   - أدخل البيانات المطلوبة
   - احفظ البيانات

2. **استيراد الموظفين من Excel:**
   - حضر ملف Excel بالتنسيق المطلوب
   - انتقل إلى "استيراد الموظفين"
   - ارفع الملف واتبع التعليمات

### حساب الرواتب
1. **إنشاء فترة رواتب جديدة:**
   - انتقل إلى إدارة الرواتب
   - أنشئ فترة جديدة
   - حدد التواريخ والإعدادات

2. **حساب الرواتب:**
   - اختر الموظفين
   - أدخل البيانات المطلوبة
   - اضغط "حساب تلقائي"
   - راجع النتائج واعتمدها

### التقارير
1. **تقرير مسير الرواتب:**
   - انتقل إلى التقارير
   - اختر "مسير الرواتب"
   - حدد الفترة والمعايير
   - صدر التقرير

## 🏗️ هيكل المشروع

```
payroll_system/
├── payroll_system/          # إعدادات Django الرئيسية
├── users/                   # إدارة المستخدمين
├── employees/               # إدارة الموظفين
├── accounting/              # النظام المحاسبي
├── payroll/                 # نظام الرواتب
├── reports/                 # التقارير والتحليلات
├── templates/               # قوالب HTML
├── static/                  # الملفات الثابتة
├── media/                   # ملفات الوسائط
├── logs/                    # ملفات السجلات
├── desktop_app.py           # تطبيق سطح المكتب
├── requirements.txt         # متطلبات Python
└── README.md               # هذا الملف
```

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية CSRF في جميع النماذج
- تدقيق شامل لجميع العمليات
- صلاحيات مستخدمين متدرجة
- نسخ احتياطية آمنة

## 📈 الأداء

- فهرسة قاعدة البيانات المحسنة
- تخزين مؤقت للاستعلامات المتكررة
- معالجة المهام الثقيلة في الخلفية
- ضغط الملفات والصور

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📞 الدعم الفني

- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +964-xxx-xxx-xxxx
- **الموقع:** www.payroll-system.com

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## 🔄 التحديثات

### الإصدار 1.0 (2024)
- إطلاق النسخة الأولى
- جميع المميزات الأساسية
- واجهة سطح المكتب والويب

### خطط مستقبلية
- تطبيق الهاتف المحمول
- تكامل مع أنظمة الحضور والانصراف
- ذكاء اصطناعي للتحليلات
- دعم لغات إضافية

---

**© 2024 النظام المحاسبي الموحد - جميع الحقوق محفوظة**
