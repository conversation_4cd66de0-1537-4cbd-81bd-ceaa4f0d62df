from django.db import models
from django.utils.translation import gettext_lazy as _
from users.models import Department

class Section(models.Model):
    section_number = models.CharField(_('رقم القسم'), max_length=50, unique=True)
    name = models.CharField(_('اسم القسم'), max_length=100)
    
    class Meta:
        verbose_name = _('القسم')
        verbose_name_plural = _('الأقسام')
    
    def __str__(self):
        return self.name

class Division(models.Model):
    division_number = models.CharField(_('رقم الشعبة'), max_length=50, unique=True)
    name = models.CharField(_('اسم الشعبة'), max_length=100)
    section = models.ForeignKey(
        Section,
        on_delete=models.CASCADE,
        verbose_name=_('القسم المرتبط')
    )
    
    class Meta:
        verbose_name = _('الشعبة')
        verbose_name_plural = _('الشعب')
    
    def __str__(self):
        return f"{self.name} - {self.section.name}"

class JobTitle(models.Model):
    title_number = models.CharField(_('رقم العنوان الوظيفي'), max_length=50, unique=True)
    name = models.CharField(_('المسمى الوظيفي'), max_length=100)
    
    class Meta:
        verbose_name = _('العنوان الوظيفي')
        verbose_name_plural = _('العناوين الوظيفية')
    
    def __str__(self):
        return self.name

class Degree(models.Model):
    degree_number = models.CharField(_('رقم الدرجة'), max_length=50, unique=True)
    name = models.CharField(_('اسم الدرجة'), max_length=100)
    
    class Meta:
        verbose_name = _('الدرجة الوظيفية')
        verbose_name_plural = _('الدرجات الوظيفية')
    
    def __str__(self):
        return self.name

class Certificate(models.Model):
    certificate_number = models.CharField(_('رقم الشهادة'), max_length=50, unique=True)
    name = models.CharField(_('اسم الشهادة'), max_length=100)
    
    class Meta:
        verbose_name = _('الشهادة')
        verbose_name_plural = _('الشهادات')
    
    def __str__(self):
        return self.name

class Stage(models.Model):
    stage_number = models.CharField(_('رقم المرحلة'), max_length=50, unique=True)
    name = models.CharField(_('اسم المرحلة'), max_length=100)
    
    class Meta:
        verbose_name = _('المرحلة')
        verbose_name_plural = _('المراحل')
    
    def __str__(self):
        return self.name

class Employee(models.Model):
    class Gender(models.TextChoices):
        MALE = 'M', _('ذكر')
        FEMALE = 'F', _('أنثى')
    
    class MaritalStatus(models.TextChoices):
        SINGLE = 'single', _('أعزب/عزباء')
        MARRIED = 'married', _('متزوج/ة')
        DIVORCED = 'divorced', _('مطلق/ة')
        WIDOWED = 'widowed', _('أرمل/ة')
    
    class EmployeeStatus(models.TextChoices):
        ACTIVE = 'active', _('مستمر')
        RETIRED = 'retired', _('متقاعد')
        DECEASED = 'deceased', _('متوفي')
        TRANSFERRED = 'transferred', _('نقل')
        SUSPENDED = 'suspended', _('متوقف')
    
    employee_number = models.CharField(_('الرقم الوظيفي'), max_length=50, unique=True)
    iban = models.CharField(_('رقم الآيبان'), max_length=50, unique=True)
    full_name = models.CharField(_('الاسم الرباعي'), max_length=200)
    gender = models.CharField(_('الجنس'), max_length=1, choices=Gender.choices)
    marital_status = models.CharField(
        _('الحالة الزوجية'),
        max_length=10,
        choices=MaritalStatus.choices
    )
    job_title = models.ForeignKey(
        JobTitle,
        on_delete=models.PROTECT,
        verbose_name=_('العنوان الوظيفي')
    )
    certificate = models.ForeignKey(
        Certificate,
        on_delete=models.PROTECT,
        verbose_name=_('الشهادة')
    )
    degree = models.ForeignKey(
        Degree,
        on_delete=models.PROTECT,
        verbose_name=_('الدرجة الوظيفية')
    )
    stage = models.ForeignKey(
        Stage,
        on_delete=models.PROTECT,
        verbose_name=_('المرحلة')
    )
    birth_date = models.DateField(_('تاريخ الميلاد'))
    hire_date = models.DateField(_('تاريخ التعيين'))
    employee_status = models.CharField(
        _('حالة الموظف'),
        max_length=15,
        choices=EmployeeStatus.choices,
        default=EmployeeStatus.ACTIVE
    )
    department = models.ForeignKey(
        Department,
        on_delete=models.PROTECT,
        verbose_name=_('الدائرة')
    )
    section = models.ForeignKey(
        Section,
        on_delete=models.PROTECT,
        verbose_name=_('القسم')
    )
    division = models.ForeignKey(
        Division,
        on_delete=models.PROTECT,
        verbose_name=_('الشعبة')
    )
    
    class Meta:
        verbose_name = _('الموظف')
        verbose_name_plural = _('الموظفون')
    
    def __str__(self):
        return f"{self.full_name} ({self.employee_number})"