from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator


class Department(models.Model):
    """دليل الأقسام"""
    name = models.CharField(_('اسم القسم'), max_length=100, unique=True)
    code = models.CharField(_('رمز القسم'), max_length=10, unique=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('القسم')
        verbose_name_plural = _('الأقسام')
        db_table = 'departments'

    def __str__(self):
        return self.name


class Division(models.Model):
    """دليل الشعب"""
    name = models.CharField(_('اسم الشعبة'), max_length=100)
    code = models.CharField(_('رمز الشعبة'), max_length=10, unique=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name=_('القسم'))
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('الشعبة')
        verbose_name_plural = _('الشعب')
        db_table = 'divisions'

    def __str__(self):
        return f"{self.name} - {self.department.name}"


class JobTitle(models.Model):
    """دليل العناوين الوظيفية"""
    name = models.CharField(_('العنوان الوظيفي'), max_length=100, unique=True)
    code = models.CharField(_('رمز العنوان'), max_length=10, unique=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('العنوان الوظيفي')
        verbose_name_plural = _('العناوين الوظيفية')
        db_table = 'job_titles'

    def __str__(self):
        return self.name


class Certificate(models.Model):
    """دليل الشهادات"""
    name = models.CharField(_('اسم الشهادة'), max_length=100, unique=True)
    code = models.CharField(_('رمز الشهادة'), max_length=10, unique=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('الشهادة')
        verbose_name_plural = _('الشهادات')
        db_table = 'certificates'

    def __str__(self):
        return self.name


class JobGrade(models.Model):
    """دليل الدرجات الوظيفية"""
    name = models.CharField(_('الدرجة الوظيفية'), max_length=100, unique=True)
    code = models.CharField(_('رمز الدرجة'), max_length=10, unique=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('الدرجة الوظيفية')
        verbose_name_plural = _('الدرجات الوظيفية')
        db_table = 'job_grades'

    def __str__(self):
        return self.name


class JobStage(models.Model):
    """دليل المراحل"""
    name = models.CharField(_('المرحلة'), max_length=100, unique=True)
    code = models.CharField(_('رمز المرحلة'), max_length=10, unique=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('المرحلة')
        verbose_name_plural = _('المراحل')
        db_table = 'job_stages'

    def __str__(self):
        return self.name


class Employee(models.Model):
    """بيانات الموظفين"""
    GENDER_CHOICES = [
        ('male', _('ذكر')),
        ('female', _('أنثى')),
    ]

    MARITAL_STATUS_CHOICES = [
        ('single', _('أعزب')),
        ('married', _('متزوج')),
        ('divorced', _('مطلق')),
        ('widowed', _('أرمل')),
    ]

    STATUS_CHOICES = [
        ('active', _('نشط')),
        ('inactive', _('غير نشط')),
        ('retired', _('متقاعد')),
        ('terminated', _('منتهي الخدمة')),
    ]

    # البيانات الأساسية
    employee_number = models.CharField(_('الرقم الوظيفي'), max_length=20, unique=True)
    iban_number = models.CharField(_('رقم الآيبان'), max_length=34, unique=True,
                                   validators=[RegexValidator(r'^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$')])
    full_name = models.CharField(_('الاسم الرباعي'), max_length=200)
    gender = models.CharField(_('الجنس'), max_length=10, choices=GENDER_CHOICES)
    marital_status = models.CharField(_('الحالة الزوجية'), max_length=10, choices=MARITAL_STATUS_CHOICES)
    birth_date = models.DateField(_('تاريخ الميلاد'))
    hire_date = models.DateField(_('تاريخ التعيين'))
    status = models.CharField(_('حالة الموظف'), max_length=15, choices=STATUS_CHOICES, default='active')

    # البيانات الوظيفية
    department = models.ForeignKey(Department, on_delete=models.PROTECT, verbose_name=_('القسم'))
    division = models.ForeignKey(Division, on_delete=models.PROTECT, verbose_name=_('الشعبة'),
                                blank=True, null=True)
    job_title = models.ForeignKey(JobTitle, on_delete=models.PROTECT, verbose_name=_('العنوان الوظيفي'))
    certificate = models.ForeignKey(Certificate, on_delete=models.PROTECT, verbose_name=_('الشهادة'))
    job_grade = models.ForeignKey(JobGrade, on_delete=models.PROTECT, verbose_name=_('الدرجة الوظيفية'))
    job_stage = models.ForeignKey(JobStage, on_delete=models.PROTECT, verbose_name=_('المرحلة'))

    # معلومات إضافية
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True, null=True)
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)

    # تواريخ النظام
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('الموظف')
        verbose_name_plural = _('الموظفون')
        db_table = 'employees'
        ordering = ['employee_number']

    def __str__(self):
        return f"{self.employee_number} - {self.full_name}"

    @property
    def age(self):
        """حساب العمر"""
        from datetime import date
        today = date.today()
        return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))

    @property
    def years_of_service(self):
        """حساب سنوات الخدمة"""
        from datetime import date
        today = date.today()
        return today.year - self.hire_date.year - ((today.month, today.day) < (self.hire_date.month, self.hire_date.day))
