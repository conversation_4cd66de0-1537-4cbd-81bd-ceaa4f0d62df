from django.urls import path
from . import views

app_name = 'accounting'

urlpatterns = [
    # الشركات
    path('companies/', views.CompanyListView.as_view(), name='company_list'),
    path('companies/create/', views.CompanyCreateView.as_view(), name='company_create'),
    path('companies/<int:pk>/edit/', views.CompanyUpdateView.as_view(), name='company_edit'),
    
    # العملات
    path('currencies/', views.CurrencyListView.as_view(), name='currency_list'),
    path('currencies/create/', views.CurrencyCreateView.as_view(), name='currency_create'),
    path('currencies/<int:pk>/edit/', views.CurrencyUpdateView.as_view(), name='currency_edit'),
    path('currencies/<int:pk>/delete/', views.CurrencyDeleteView.as_view(), name='currency_delete'),
    
    # الفترات المحاسبية
    path('fiscal-periods/', views.FiscalPeriodListView.as_view(), name='fiscal_period_list'),
    path('fiscal-periods/create/', views.FiscalPeriodCreateView.as_view(), name='fiscal_period_create'),
    path('fiscal-periods/<int:pk>/edit/', views.FiscalPeriodUpdateView.as_view(), name='fiscal_period_edit'),
    path('fiscal-periods/<int:pk>/delete/', views.FiscalPeriodDeleteView.as_view(), name='fiscal_period_delete'),
    
    # دليل الحسابات
    path('accounts/', views.AccountListView.as_view(), name='account_list'),
    path('accounts/create/', views.AccountCreateView.as_view(), name='account_create'),
    path('accounts/<int:pk>/', views.AccountDetailView.as_view(), name='account_detail'),
    path('accounts/<int:pk>/edit/', views.AccountUpdateView.as_view(), name='account_edit'),
    path('accounts/<int:pk>/delete/', views.AccountDeleteView.as_view(), name='account_delete'),
    
    # أنواع الحسابات
    path('account-types/', views.AccountTypeListView.as_view(), name='account_type_list'),
    path('account-types/create/', views.AccountTypeCreateView.as_view(), name='account_type_create'),
    path('account-types/<int:pk>/edit/', views.AccountTypeUpdateView.as_view(), name='account_type_edit'),
    path('account-types/<int:pk>/delete/', views.AccountTypeDeleteView.as_view(), name='account_type_delete'),
    
    # الصناديق
    path('cash-boxes/', views.CashBoxListView.as_view(), name='cash_box_list'),
    path('cash-boxes/create/', views.CashBoxCreateView.as_view(), name='cash_box_create'),
    path('cash-boxes/<int:pk>/edit/', views.CashBoxUpdateView.as_view(), name='cash_box_edit'),
    path('cash-boxes/<int:pk>/delete/', views.CashBoxDeleteView.as_view(), name='cash_box_delete'),
    
    # المصارف
    path('banks/', views.BankListView.as_view(), name='bank_list'),
    path('banks/create/', views.BankCreateView.as_view(), name='bank_create'),
    path('banks/<int:pk>/edit/', views.BankUpdateView.as_view(), name='bank_edit'),
    path('banks/<int:pk>/delete/', views.BankDeleteView.as_view(), name='bank_delete'),
    
    # فروع المصارف
    path('bank-branches/', views.BankBranchListView.as_view(), name='bank_branch_list'),
    path('bank-branches/create/', views.BankBranchCreateView.as_view(), name='bank_branch_create'),
    path('bank-branches/<int:pk>/edit/', views.BankBranchUpdateView.as_view(), name='bank_branch_edit'),
    path('bank-branches/<int:pk>/delete/', views.BankBranchDeleteView.as_view(), name='bank_branch_delete'),
    
    # الحسابات البنكية
    path('bank-accounts/', views.BankAccountListView.as_view(), name='bank_account_list'),
    path('bank-accounts/create/', views.BankAccountCreateView.as_view(), name='bank_account_create'),
    path('bank-accounts/<int:pk>/', views.BankAccountDetailView.as_view(), name='bank_account_detail'),
    path('bank-accounts/<int:pk>/edit/', views.BankAccountUpdateView.as_view(), name='bank_account_edit'),
    path('bank-accounts/<int:pk>/delete/', views.BankAccountDeleteView.as_view(), name='bank_account_delete'),
    
    # مراكز التكلفة
    path('cost-centers/', views.CostCenterListView.as_view(), name='cost_center_list'),
    path('cost-centers/create/', views.CostCenterCreateView.as_view(), name='cost_center_create'),
    path('cost-centers/<int:pk>/edit/', views.CostCenterUpdateView.as_view(), name='cost_center_edit'),
    path('cost-centers/<int:pk>/delete/', views.CostCenterDeleteView.as_view(), name='cost_center_delete'),
]
