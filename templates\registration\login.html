{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block title %}تسجيل الدخول - المحاسب الشامل{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/auth.css' %}">
{% endblock %}

{% block content %}
<div class="login-page d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-5">
                <div class="login-card p-4">
                    <!-- Logo and Title -->
                    <div class="text-center mb-4">
                        <img src="{% static 'img/logo.png' %}" alt="المحاسب الشامل" class="login-logo">
                        <h2 class="login-title">المحاسب الشامل</h2>
                    </div>

                    <!-- Login Form -->
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        {% if form.errors %}
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            اسم المستخدم أو كلمة المرور غير صحيحة
                        </div>
                        {% endif %}

                        {% if messages %}
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}" role="alert">
                            {{ message }}
                        </div>
                        {% endfor %}
                        {% endif %}

                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" name="username" id="username" class="form-control" required>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" name="password" id="password" class="form-control" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>

                        {% if next %}
                        <input type="hidden" name="next" value="{{ next }}">
                        {% endif %}
                    </form>
                </div>

                <!-- Footer -->
                <div class="text-center mt-4 text-white">
                    <small>&copy; {% now "Y" %} المحاسب الشامل. جميع الحقوق محفوظة.</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');

    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %}