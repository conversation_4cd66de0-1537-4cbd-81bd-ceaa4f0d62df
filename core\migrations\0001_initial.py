# Generated by Django 4.2.7 on 2025-06-06 08:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AccountGuide",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "account_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الحساب"
                    ),
                ),
                (
                    "account_name",
                    models.CharField(max_length=200, verbose_name="اسم الحساب"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="الملاحظات")),
            ],
            options={
                "verbose_name": "دليل الحساب",
                "verbose_name_plural": "دليل الحسابات",
            },
        ),
        migrations.CreateModel(
            name="AccountingPeriod",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "period_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الفترة"
                    ),
                ),
                (
                    "fiscal_year",
                    models.PositiveIntegerField(verbose_name="السنة المالية"),
                ),
                (
                    "is_current",
                    models.BooleanField(default=False, verbose_name="الفترة الحالية"),
                ),
                (
                    "months_count",
                    models.PositiveSmallIntegerField(verbose_name="عدد الأشهر"),
                ),
                (
                    "start_month",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 1),
                            (2, 2),
                            (3, 3),
                            (4, 4),
                            (5, 5),
                            (6, 6),
                            (7, 7),
                            (8, 8),
                            (9, 9),
                            (10, 10),
                            (11, 11),
                            (12, 12),
                        ],
                        verbose_name="من شهر",
                    ),
                ),
                (
                    "end_month",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 1),
                            (2, 2),
                            (3, 3),
                            (4, 4),
                            (5, 5),
                            (6, 6),
                            (7, 7),
                            (8, 8),
                            (9, 9),
                            (10, 10),
                            (11, 11),
                            (12, 12),
                        ],
                        verbose_name="إلى شهر",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="الملاحظات")),
            ],
            options={
                "verbose_name": "الفترة المحاسبية",
                "verbose_name_plural": "الفترات المحاسبية",
            },
        ),
        migrations.CreateModel(
            name="Activity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        help_text="وصف النشاط الذي قام به المستخدم",
                        max_length=255,
                        verbose_name="النشاط",
                    ),
                ),
                (
                    "details",
                    models.TextField(
                        blank=True,
                        help_text="تفاصيل إضافية عن النشاط",
                        verbose_name="التفاصيل",
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True,
                        help_text="عنوان IP للمستخدم عند تنفيذ النشاط",
                        null=True,
                        verbose_name="عنوان IP",
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(
                        blank=True,
                        help_text="معلومات عن متصفح المستخدم",
                        verbose_name="متصفح المستخدم",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ النشاط"
                    ),
                ),
            ],
            options={
                "verbose_name": "النشاط",
                "verbose_name_plural": "النشاطات",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Bank",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "bank_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم المصرف"
                    ),
                ),
                (
                    "bank_name",
                    models.CharField(max_length=200, verbose_name="اسم المصرف"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="الملاحظات")),
            ],
            options={
                "verbose_name": "المصرف",
                "verbose_name_plural": "المصارف",
            },
        ),
        migrations.CreateModel(
            name="BankAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "account_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الحساب"
                    ),
                ),
                (
                    "account_name",
                    models.CharField(max_length=200, verbose_name="اسم الحساب البنكي"),
                ),
                (
                    "opening_balance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=15,
                        verbose_name="الرصيد الافتتاحي",
                    ),
                ),
                (
                    "current_balance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=15,
                        verbose_name="الرصيد الحالي",
                    ),
                ),
                (
                    "bank",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.bank",
                        verbose_name="المصرف",
                    ),
                ),
            ],
            options={
                "verbose_name": "الحساب البنكي",
                "verbose_name_plural": "الحسابات البنكية",
            },
        ),
        migrations.CreateModel(
            name="Currency",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "currency_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم العملة"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم العملة")),
                ("symbol", models.CharField(max_length=10, verbose_name="رمز العملة")),
                (
                    "fraction_name",
                    models.CharField(max_length=100, verbose_name="أجزاء العملة"),
                ),
                (
                    "currency_type",
                    models.CharField(
                        choices=[("local", "محلية"), ("foreign", "أجنبية")],
                        default="local",
                        max_length=7,
                        verbose_name="نوع العملة",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="الملاحظات")),
            ],
            options={
                "verbose_name": "العملة",
                "verbose_name_plural": "العملات",
            },
        ),
        migrations.CreateModel(
            name="FinalAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "account_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الحساب"
                    ),
                ),
                (
                    "account_name",
                    models.CharField(max_length=200, verbose_name="اسم الحساب"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="الملاحظات")),
            ],
            options={
                "verbose_name": "الحساب الختامي",
                "verbose_name_plural": "الحسابات الختامية",
            },
        ),
        migrations.CreateModel(
            name="Fund",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "fund_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الصندوق"
                    ),
                ),
                (
                    "fund_name",
                    models.CharField(max_length=200, verbose_name="اسم الصندوق"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="الملاحظات")),
            ],
            options={
                "verbose_name": "الصندوق",
                "verbose_name_plural": "الصناديق",
            },
        ),
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "organization_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم المؤسسة"
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم المؤسسة")),
                ("address", models.TextField(verbose_name="العنوان")),
                (
                    "email",
                    models.EmailField(max_length=254, verbose_name="البريد الإلكتروني"),
                ),
                (
                    "website",
                    models.URLField(blank=True, verbose_name="الموقع الإلكتروني"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="الملاحظات")),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        upload_to="organization_logos/",
                        verbose_name="شعار المؤسسة",
                    ),
                ),
            ],
            options={
                "verbose_name": "المؤسسة",
                "verbose_name_plural": "المؤسسات",
            },
        ),
        migrations.CreateModel(
            name="SystemLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("info", "معلومات"),
                            ("warning", "تحذير"),
                            ("error", "خطأ"),
                            ("critical", "خطأ حرج"),
                        ],
                        default="info",
                        max_length=10,
                        verbose_name="المستوى",
                    ),
                ),
                (
                    "message",
                    models.TextField(help_text="وصف الحدث", verbose_name="الرسالة"),
                ),
                (
                    "traceback",
                    models.TextField(
                        blank=True,
                        help_text="تفاصيل تقنية عن الخطأ إن وجدت",
                        verbose_name="تتبع الخطأ",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الحدث"),
                ),
            ],
            options={
                "verbose_name": "سجل النظام",
                "verbose_name_plural": "سجلات النظام",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["-created_at"], name="core_system_created_19c977_idx"
                    ),
                    models.Index(
                        fields=["level", "-created_at"],
                        name="core_system_level_556847_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="FinancialTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transaction_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم المعاملة"
                    ),
                ),
                ("transaction_date", models.DateField(verbose_name="تاريخ المعاملة")),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("payment", "سند صرف"),
                            ("receipt", "سند قبض"),
                            ("journal", "قيد يومية"),
                        ],
                        max_length=10,
                        verbose_name="نوع المعاملة",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=3, max_digits=15, verbose_name="المبلغ"
                    ),
                ),
                ("description", models.TextField(verbose_name="الوصف")),
                ("notes", models.TextField(blank=True, verbose_name="الملاحظات")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="core.accountguide",
                        verbose_name="الحساب",
                    ),
                ),
                (
                    "bank_account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="core.bankaccount",
                        verbose_name="الحساب البنكي",
                    ),
                ),
            ],
            options={
                "verbose_name": "المعاملة المالية",
                "verbose_name_plural": "المعاملات المالية",
                "ordering": ["-transaction_date", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="BankBranch",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "branch_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الفرع"
                    ),
                ),
                (
                    "branch_name",
                    models.CharField(max_length=200, verbose_name="اسم الفرع"),
                ),
                (
                    "bank",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.bank",
                        verbose_name="المصرف",
                    ),
                ),
                (
                    "bank_account",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="core.bankaccount",
                        verbose_name="الحساب البنكي",
                    ),
                ),
            ],
            options={
                "verbose_name": "فرع المصرف",
                "verbose_name_plural": "فروع المصارف",
            },
        ),
        migrations.AddField(
            model_name="bankaccount",
            name="currency",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="core.currency",
                verbose_name="العملة",
            ),
        ),
    ]
