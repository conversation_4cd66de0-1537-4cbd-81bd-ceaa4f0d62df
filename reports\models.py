from django.db import models
from django.utils.translation import gettext_lazy as _
from users.models import CustomUser


class ReportTemplate(models.Model):
    """قوالب التقارير"""
    REPORT_TYPES = [
        ('payroll', _('تقارير الرواتب')),
        ('financial', _('تقارير مالية')),
        ('employee', _('تقارير الموظفين')),
        ('custom', _('تقارير مخصصة')),
    ]

    name = models.CharField(_('اسم التقرير'), max_length=200)
    report_type = models.CharField(_('نوع التقرير'), max_length=20, choices=REPORT_TYPES)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    sql_query = models.TextField(_('استعلام SQL'), blank=True, null=True)
    parameters = models.JSONField(_('المعاملات'), default=dict, blank=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name=_('أنشئ بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('قالب التقرير')
        verbose_name_plural = _('قوالب التقارير')
        db_table = 'report_templates'

    def __str__(self):
        return self.name


class ReportExecution(models.Model):
    """تنفيذ التقارير"""
    STATUS_CHOICES = [
        ('pending', _('في الانتظار')),
        ('running', _('قيد التنفيذ')),
        ('completed', _('مكتمل')),
        ('failed', _('فشل')),
    ]

    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, verbose_name=_('قالب التقرير'))
    executed_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name=_('نفذ بواسطة'))
    parameters_used = models.JSONField(_('المعاملات المستخدمة'), default=dict)
    status = models.CharField(_('الحالة'), max_length=15, choices=STATUS_CHOICES, default='pending')
    result_file = models.FileField(_('ملف النتيجة'), upload_to='reports/', blank=True, null=True)
    error_message = models.TextField(_('رسالة الخطأ'), blank=True, null=True)
    execution_time = models.DurationField(_('وقت التنفيذ'), null=True, blank=True)
    created_at = models.DateTimeField(_('تاريخ التنفيذ'), auto_now_add=True)

    class Meta:
        verbose_name = _('تنفيذ التقرير')
        verbose_name_plural = _('تنفيذات التقارير')
        db_table = 'report_executions'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.template.name} - {self.created_at}"


class Dashboard(models.Model):
    """لوحات المعلومات"""
    name = models.CharField(_('اسم لوحة المعلومات'), max_length=200)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    layout = models.JSONField(_('تخطيط اللوحة'), default=dict)
    is_default = models.BooleanField(_('افتراضية'), default=False)
    is_active = models.BooleanField(_('نشطة'), default=True)
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name=_('أنشئت بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('لوحة المعلومات')
        verbose_name_plural = _('لوحات المعلومات')
        db_table = 'dashboards'

    def __str__(self):
        return self.name


class DashboardWidget(models.Model):
    """عناصر لوحة المعلومات"""
    WIDGET_TYPES = [
        ('chart', _('رسم بياني')),
        ('table', _('جدول')),
        ('kpi', _('مؤشر أداء')),
        ('text', _('نص')),
    ]

    dashboard = models.ForeignKey(Dashboard, on_delete=models.CASCADE, verbose_name=_('لوحة المعلومات'))
    name = models.CharField(_('اسم العنصر'), max_length=200)
    widget_type = models.CharField(_('نوع العنصر'), max_length=20, choices=WIDGET_TYPES)
    data_source = models.TextField(_('مصدر البيانات'))
    configuration = models.JSONField(_('التكوين'), default=dict)
    position_x = models.IntegerField(_('الموضع الأفقي'), default=0)
    position_y = models.IntegerField(_('الموضع العمودي'), default=0)
    width = models.IntegerField(_('العرض'), default=4)
    height = models.IntegerField(_('الارتفاع'), default=3)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('عنصر لوحة المعلومات')
        verbose_name_plural = _('عناصر لوحات المعلومات')
        db_table = 'dashboard_widgets'

    def __str__(self):
        return f"{self.dashboard.name} - {self.name}"
