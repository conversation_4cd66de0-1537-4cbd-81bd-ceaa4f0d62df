from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    # قوالب التقارير
    path('', views.ReportTemplateListView.as_view(), name='template_list'),
    path('templates/create/', views.ReportTemplateCreateView.as_view(), name='template_create'),
    path('templates/<int:pk>/', views.ReportTemplateDetailView.as_view(), name='template_detail'),
    path('templates/<int:pk>/edit/', views.ReportTemplateUpdateView.as_view(), name='template_edit'),
    path('templates/<int:pk>/delete/', views.ReportTemplateDeleteView.as_view(), name='template_delete'),
    path('templates/<int:pk>/execute/', views.ReportExecuteView.as_view(), name='template_execute'),
    
    # تنفيذ التقارير
    path('executions/', views.ReportExecutionListView.as_view(), name='execution_list'),
    path('executions/<int:pk>/', views.ReportExecutionDetailView.as_view(), name='execution_detail'),
    path('executions/<int:pk>/download/', views.ReportDownloadView.as_view(), name='execution_download'),
    
    # لوحات المعلومات
    path('dashboards/', views.DashboardListView.as_view(), name='dashboard_list'),
    path('dashboards/create/', views.DashboardCreateView.as_view(), name='dashboard_create'),
    path('dashboards/<int:pk>/', views.DashboardDetailView.as_view(), name='dashboard_detail'),
    path('dashboards/<int:pk>/edit/', views.DashboardUpdateView.as_view(), name='dashboard_edit'),
    path('dashboards/<int:pk>/delete/', views.DashboardDeleteView.as_view(), name='dashboard_delete'),
    
    # عناصر لوحة المعلومات
    path('dashboards/<int:dashboard_id>/widgets/create/', views.DashboardWidgetCreateView.as_view(), name='widget_create'),
    path('widgets/<int:pk>/edit/', views.DashboardWidgetUpdateView.as_view(), name='widget_edit'),
    path('widgets/<int:pk>/delete/', views.DashboardWidgetDeleteView.as_view(), name='widget_delete'),
    path('widgets/<int:pk>/data/', views.WidgetDataView.as_view(), name='widget_data'),
    
    # تقارير جاهزة
    path('payroll-summary/', views.PayrollSummaryReportView.as_view(), name='payroll_summary'),
    path('employee-list/', views.EmployeeListReportView.as_view(), name='employee_list_report'),
    path('financial-summary/', views.FinancialSummaryReportView.as_view(), name='financial_summary'),
    path('tax-report/', views.TaxReportView.as_view(), name='tax_report'),
    path('bank-transfer/', views.BankTransferReportView.as_view(), name='bank_transfer'),
    
    # تصدير
    path('export/payroll/<int:period_id>/', views.ExportPayrollView.as_view(), name='export_payroll'),
    path('export/employees/', views.ExportEmployeesView.as_view(), name='export_employees'),
    path('export/financial/', views.ExportFinancialView.as_view(), name='export_financial'),
]
