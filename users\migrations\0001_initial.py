# Generated by Django 4.2.7 on 2025-06-06 08:31

import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="Department",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "department_number",
                    models.Char<PERSON>ield(
                        max_length=50, unique=True, verbose_name="رقم الدائرة"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الدائرة")),
                ("address", models.CharField(max_length=255, verbose_name="العنوان")),
                ("phone", models.Char<PERSON>ield(max_length=20, verbose_name="رقم الهاتف")),
                (
                    "email",
                    models.EmailField(max_length=254, verbose_name="البريد الإلكتروني"),
                ),
            ],
            options={
                "verbose_name": "الدائرة",
                "verbose_name_plural": "الدوائر",
            },
        ),
        migrations.CreateModel(
            name="UserGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "group_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم المجموعة"
                    ),
                ),
                (
                    "group_name",
                    models.CharField(max_length=100, verbose_name="اسم المجموعة"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="الملاحظات")),
            ],
            options={
                "verbose_name": "مجموعة المستخدمين",
                "verbose_name_plural": "مجموعات المستخدمين",
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="email address"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "account_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الحساب"
                    ),
                ),
                (
                    "account_name",
                    models.CharField(max_length=100, verbose_name="اسم الحساب"),
                ),
                (
                    "account_type",
                    models.CharField(
                        choices=[("admin", "مدير"), ("user", "مستخدم")],
                        default="user",
                        max_length=5,
                        verbose_name="نوع الحساب",
                    ),
                ),
                (
                    "department",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.department",
                        verbose_name="الدائرة",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_group",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="users.usergroup",
                        verbose_name="مجموعة المستخدمين",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "المستخدم",
                "verbose_name_plural": "المستخدمون",
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
