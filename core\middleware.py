from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.utils.translation import activate
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from .activity_models import Activity, SystemLog
import threading

# متغير عام لتخزين معلومات الطلب الحالي
_thread_locals = threading.local()

def get_current_request():
    """الحصول على كائن الطلب الحالي"""
    return getattr(_thread_locals, 'request', None)

def get_current_user():
    """الحصول على المستخدم الحالي"""
    request = get_current_request()
    if request:
        return getattr(request, 'user', None)
    return None

class RequestMiddleware(MiddlewareMixin):
    """وسيط لتخزين كائن الطلب الحالي"""
    
    def process_request(self, request):
        _thread_locals.request = request
    
    def process_response(self, request, response):
        if hasattr(_thread_locals, 'request'):
            del _thread_locals.request
        return response

class ActivityTrackingMiddleware(MiddlewareMixin):
    """وسيط لتتبع نشاطات المستخدمين"""
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.get_response = get_response
    
    def process_request(self, request):
        request.start_time = timezone.now()
    
    def process_response(self, request, response):
        if hasattr(request, 'user') and request.user.is_authenticated:
            # تجاهل طلبات الملفات الثابتة والوسائط
            if not any(path in request.path for path in ['/static/', '/media/']):
                duration = timezone.now() - getattr(request, 'start_time', timezone.now())
                
                # تسجيل النشاط
                Activity.objects.create(
                    user=request.user,
                    action=f"{request.method} {request.path}",
                    ip_address=request.META.get('REMOTE_ADDR'),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    details=f"Duration: {duration.total_seconds():.2f}s"
                )
        
        return response
    
    def process_exception(self, request, exception):
        if hasattr(request, 'user') and request.user.is_authenticated:
            # تسجيل الخطأ
            SystemLog.error(
                f"Error in {request.path}",
                str(exception)
            )
        return None

class LanguageMiddleware(MiddlewareMixin):
    """وسيط لإدارة لغة المستخدم"""
    
    def process_request(self, request):
        # تفعيل اللغة العربية افتراضياً
        activate('ar')
        
        # السماح للمستخدم بتغيير اللغة
        user_language = request.session.get('django_language', 'ar')
        activate(user_language)

class MaintenanceModeMiddleware(MiddlewareMixin):
    """وسيط لوضع الصيانة"""
    
    def process_request(self, request):
        # التحقق من وضع الصيانة في الإعدادات
        from django.conf import settings
        if getattr(settings, 'MAINTENANCE_MODE', False):
            if not request.user.is_superuser:
                messages.warning(
                    request,
                    _('النظام في وضع الصيانة حالياً. يرجى المحاولة لاحقاً.')
                )
                from django.shortcuts import render
                return render(request, 'maintenance.html', status=503)

class DatabaseBackupMiddleware(MiddlewareMixin):
    """وسيط للنسخ الاحتياطي التلقائي"""
    
    def process_request(self, request):
        # التحقق من تاريخ آخر نسخة احتياطية
        from django.core.cache import cache
        last_backup = cache.get('last_database_backup')
        
        if not last_backup or (timezone.now() - last_backup).days >= 1:
            try:
                from django.core.management import call_command
                import os
                
                # إنشاء نسخة احتياطية
                backup_dir = 'backups'
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)
                
                filename = f'backup_{timezone.now().strftime("%Y%m%d_%H%M%S")}.json'
                with open(f'{backup_dir}/{filename}', 'w') as f:
                    call_command('dumpdata', stdout=f)
                
                # تحديث تاريخ آخر نسخة احتياطية
                cache.set('last_database_backup', timezone.now())
                
                SystemLog.info(f'تم إنشاء نسخة احتياطية: {filename}')
                
            except Exception as e:
                SystemLog.error('خطأ في إنشاء النسخة الاحتياطية', str(e))