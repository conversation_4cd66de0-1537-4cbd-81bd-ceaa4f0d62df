{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}404 - الصفحة غير موجودة{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <h1 class="display-1 text-primary mb-4">404</h1>
                <img src="{% static 'img/404.svg' %}" alt="404" class="img-fluid mb-4" style="max-width: 300px;">
                <h2 class="mb-4">عذراً، الصفحة غير موجودة</h2>
                <p class="text-muted mb-4">
                    الصفحة التي تبحث عنها قد تم نقلها أو حذفها أو تغيير عنوانها.
                </p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="{% url 'dashboard' %}" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        الرجوع للرئيسية
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        الرجوع للصفحة السابقة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 3rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.error-page h1 {
    font-size: 6rem;
    font-weight: bold;
    color: var(--primary-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-page h2 {
    color: var(--dark-color);
    font-weight: bold;
}

.error-page .btn {
    padding: 0.75rem 1.5rem;
    font-weight: bold;
}

@media (max-width: 768px) {
    .error-page {
        padding: 2rem;
    }
    
    .error-page h1 {
        font-size: 4rem;
    }
}
</style>
{% endblock %}