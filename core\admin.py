from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from .models import Organization  # Currency معطل مؤقتاً
# from .financial_models import (  # معطل مؤقتاً
#     AccountGuide, Fund, Bank, BankBranch,
#     BankAccount, FinancialTransaction
# )
# from .activity_models import Activity, SystemLog  # معطل مؤقتاً

@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'website']
    search_fields = ['name', 'email', 'website']

    def has_add_permission(self, request):
        # السماح بإضافة مؤسسة واحدة فقط
        return not Organization.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # منع حذف المؤسسة
        return False

    # def display_logo(self, obj):  # معطل مؤقتاً
    #     if obj.logo:
    #         return format_html(
    #             '<img src="{}" width="50" height="50" />',
    #             obj.logo.url
    #         )
    #     return '-'
    # display_logo.short_description = _('الشعار')

# النماذج التالية معطلة مؤقتاً حتى يتم إصلاح المشاكل

# @admin.register(Currency)
# class CurrencyAdmin(admin.ModelAdmin):
#     list_display = ['currency_number', 'name', 'symbol', 'currency_type']
#     list_filter = ['currency_type']
#     search_fields = ['currency_number', 'name', 'symbol']

# باقي النماذج معطلة مؤقتاً...
