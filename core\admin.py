from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from .models import Organization, Currency
from .financial_models import (
    AccountGuide, Fund, Bank, BankBranch,
    BankAccount, FinancialTransaction
)
from .activity_models import Activity, SystemLog

@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'website', 'display_logo']
    search_fields = ['name', 'email', 'website']
    
    def display_logo(self, obj):
        if obj.logo:
            return format_html(
                '<img src="{}" width="50" height="50" />',
                obj.logo.url
            )
        return '-'
    display_logo.short_description = _('الشعار')

@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['currency_number', 'name', 'symbol', 'currency_type']
    list_filter = ['currency_type']
    search_fields = ['currency_number', 'name', 'symbol']

@admin.register(AccountGuide)
class AccountGuideAdmin(admin.ModelAdmin):
    list_display = ['account_number', 'account_name', 'parent']
    list_filter = ['parent']
    search_fields = ['account_number', 'account_name']
    raw_id_fields = ['parent']

@admin.register(Fund)
class FundAdmin(admin.ModelAdmin):
    list_display = ['fund_number', 'fund_name']
    search_fields = ['fund_number', 'fund_name']

@admin.register(Bank)
class BankAdmin(admin.ModelAdmin):
    list_display = ['bank_number', 'bank_name']
    search_fields = ['bank_number', 'bank_name']

class BankBranchInline(admin.TabularInline):
    model = BankBranch
    extra = 1

@admin.register(BankBranch)
class BankBranchAdmin(admin.ModelAdmin):
    list_display = ['branch_number', 'branch_name', 'bank', 'bank_account']
    list_filter = ['bank']
    search_fields = ['branch_number', 'branch_name']
    raw_id_fields = ['bank', 'bank_account']

@admin.register(BankAccount)
class BankAccountAdmin(admin.ModelAdmin):
    list_display = [
        'account_number',
        'account_name',
        'bank',
        'currency',
        'current_balance'
    ]
    list_filter = ['bank', 'currency']
    search_fields = ['account_number', 'account_name']
    readonly_fields = ['current_balance']

@admin.register(FinancialTransaction)
class FinancialTransactionAdmin(admin.ModelAdmin):
    list_display = [
        'transaction_number',
        'transaction_date',
        'transaction_type',
        'amount',
        'bank_account',
        'account'
    ]
    list_filter = ['transaction_type', 'transaction_date', 'bank_account']
    search_fields = ['transaction_number', 'description']
    raw_id_fields = ['bank_account', 'account']
    date_hierarchy = 'transaction_date'
    
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (_('معلومات المعاملة'), {
            'fields': (
                'transaction_number',
                'transaction_date',
                'transaction_type'
            )
        }),
        (_('التفاصيل المالية'), {
            'fields': (
                'amount',
                'description',
                'bank_account',
                'account'
            )
        }),
        (_('معلومات إضافية'), {
            'fields': (
                'notes',
                'created_at',
                'updated_at'
            ),
            'classes': ('collapse',)
        }),
    )

@admin.register(Activity)
class ActivityAdmin(admin.ModelAdmin):
    list_display = [
        'user',
        'action',
        'ip_address',
        'created_at'
    ]
    list_filter = ['created_at', 'user']
    search_fields = ['action', 'details', 'user__username']
    readonly_fields = [
        'user',
        'action',
        'details',
        'ip_address',
        'user_agent',
        'created_at'
    ]
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False

@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = [
        'created_at',
        'level',
        'message_preview'
    ]
    list_filter = ['level', 'created_at']
    search_fields = ['message', 'traceback']
    readonly_fields = ['level', 'message', 'traceback', 'created_at']
    
    def message_preview(self, obj):
        return obj.message[:100] + '...' if len(obj.message) > 100 else obj.message
    message_preview.short_description = _('الرسالة')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        # السماح بحذف السجلات القديمة فقط للمشرفين
        return request.user.is_superuser
