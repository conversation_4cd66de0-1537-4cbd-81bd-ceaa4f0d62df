# Generated by Django 4.2.7 on 2025-06-05 17:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("reports", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="reporttemplate",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to=settings.AUTH_USER_MODEL,
                verbose_name="أنشئ بواسطة",
            ),
        ),
        migrations.AddField(
            model_name="reportexecution",
            name="executed_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to=settings.AUTH_USER_MODEL,
                verbose_name="نفذ بواسطة",
            ),
        ),
        migrations.AddField(
            model_name="reportexecution",
            name="template",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="reports.reporttemplate",
                verbose_name="قالب التقرير",
            ),
        ),
        migrations.AddField(
            model_name="dashboardwidget",
            name="dashboard",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="reports.dashboard",
                verbose_name="لوحة المعلومات",
            ),
        ),
        migrations.AddField(
            model_name="dashboard",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to=settings.AUTH_USER_MODEL,
                verbose_name="أنشئت بواسطة",
            ),
        ),
    ]
