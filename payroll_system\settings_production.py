"""
إعدادات الإنتاج للنظام المحاسبي الموحد
"""

from .settings import *
import os

# إعدادات الأمان
DEBUG = False
ALLOWED_HOSTS = ['*']  # يجب تحديد النطاقات المسموحة في الإنتاج

# قاعدة البيانات للإنتاج
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME', 'payroll_system'),
        'USER': os.environ.get('DB_USER', 'payroll_user'),
        'PASSWORD': os.environ.get('DB_PASSWORD', 'payroll_password'),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# إعدادات الأمان المتقدمة
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_HSTS_SECONDS = 31536000
SECURE_REDIRECT_EXEMPT = []
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
X_FRAME_OPTIONS = 'DENY'

# إعدادات الملفات الثابتة
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# إعدادات البريد الإلكتروني
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

# إعدادات Celery للإنتاج
CELERY_BROKER_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Asia/Baghdad'

# إعدادات التخزين المؤقت
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# إعدادات الجلسات
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
SESSION_COOKIE_AGE = 3600  # ساعة واحدة

# إعدادات السجلات المتقدمة
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'payroll_system.log'),
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'errors.log'),
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'mail_admins': {
            'level': 'ERROR',
            'filters': ['require_debug_false'],
            'class': 'django.utils.log.AdminEmailHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['file'],
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['error_file', 'mail_admins'],
            'level': 'ERROR',
            'propagate': False,
        },
        'payroll_system': {
            'handlers': ['file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# إعدادات المراقبة (Sentry)
if os.environ.get('SENTRY_DSN'):
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration
    from sentry_sdk.integrations.celery import CeleryIntegration
    
    sentry_sdk.init(
        dsn=os.environ.get('SENTRY_DSN'),
        integrations=[
            DjangoIntegration(auto_enabling=True),
            CeleryIntegration(auto_enabling=True),
        ],
        traces_sample_rate=0.1,
        send_default_pii=True,
        environment=os.environ.get('ENVIRONMENT', 'production'),
    )

# إعدادات الأداء
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB

# إعدادات قاعدة البيانات المحسنة
DATABASES['default']['CONN_MAX_AGE'] = 60
DATABASES['default']['OPTIONS'].update({
    'MAX_CONNS': 20,
    'charset': 'utf8mb4',
})

# إعدادات النسخ الاحتياطية
BACKUP_SETTINGS = {
    'BACKUP_DIR': os.path.join(BASE_DIR, 'backups'),
    'RETENTION_DAYS': 30,
    'COMPRESS': True,
    'ENCRYPT': True,
    'ENCRYPTION_KEY': os.environ.get('BACKUP_ENCRYPTION_KEY', ''),
}

# إعدادات التكامل مع الأنظمة الخارجية
EXTERNAL_SYSTEMS = {
    'ATTENDANCE_SYSTEM': {
        'ENABLED': os.environ.get('ATTENDANCE_SYSTEM_ENABLED', 'False').lower() == 'true',
        'API_URL': os.environ.get('ATTENDANCE_API_URL', ''),
        'API_KEY': os.environ.get('ATTENDANCE_API_KEY', ''),
    },
    'BANK_INTEGRATION': {
        'ENABLED': os.environ.get('BANK_INTEGRATION_ENABLED', 'False').lower() == 'true',
        'API_URL': os.environ.get('BANK_API_URL', ''),
        'API_KEY': os.environ.get('BANK_API_KEY', ''),
    },
}

# إعدادات الإشعارات
NOTIFICATION_SETTINGS = {
    'EMAIL_NOTIFICATIONS': True,
    'SMS_NOTIFICATIONS': False,
    'PUSH_NOTIFICATIONS': False,
    'ADMIN_EMAIL': os.environ.get('ADMIN_EMAIL', '<EMAIL>'),
}

# إعدادات التقارير
REPORT_SETTINGS = {
    'MAX_EXPORT_ROWS': 50000,
    'CACHE_TIMEOUT': 3600,  # ساعة واحدة
    'ASYNC_GENERATION': True,
}

# إعدادات الأمان الإضافية
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
USE_TZ = True

# إعدادات CORS للإنتاج
CORS_ALLOWED_ORIGINS = [
    "https://payroll-system.com",
    "https://www.payroll-system.com",
]

CORS_ALLOW_CREDENTIALS = True

# إعدادات WhiteNoise للملفات الثابتة
MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')

# تحسين الأداء
CONN_MAX_AGE = 60

# إعدادات المهلة الزمنية
TIMEOUT_SETTINGS = {
    'REQUEST_TIMEOUT': 30,
    'DATABASE_TIMEOUT': 20,
    'CACHE_TIMEOUT': 300,
}
