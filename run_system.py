#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النظام المحاسبي الموحد - ملف التشغيل السريع
يمكن استخدام هذا الملف لتشغيل النظام بسرعة
"""

import os
import sys
import subprocess
import threading
import time
from pathlib import Path

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    النظام المحاسبي الموحد                    ║
    ║                      رواتب الموظفين                        ║
    ║                                                              ║
    ║                      الإصدار 1.0                           ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    # فحص Python
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} متوفر")
    
    # فحص pip
    try:
        import pip
        print("✅ pip متوفر")
    except ImportError:
        print("❌ خطأ: pip غير متوفر")
        return False
    
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ خطأ: ملف requirements.txt غير موجود")
        return False
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    
    try:
        # إنشاء migrations
        subprocess.run([sys.executable, "manage.py", "makemigrations"], 
                      check=True, capture_output=True)
        print("✅ تم إنشاء migrations")
        
        # تطبيق migrations
        subprocess.run([sys.executable, "manage.py", "migrate"], 
                      check=True, capture_output=True)
        print("✅ تم تطبيق migrations")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def create_superuser():
    """إنشاء مستخدم إداري"""
    print("👤 إنشاء مستخدم إداري...")
    
    try:
        # فحص وجود مستخدم إداري
        result = subprocess.run([sys.executable, "manage.py", "shell", "-c", 
                               "from django.contrib.auth import get_user_model; "
                               "User = get_user_model(); "
                               "print(User.objects.filter(is_superuser=True).exists())"], 
                              capture_output=True, text=True)
        
        if "True" in result.stdout:
            print("✅ يوجد مستخدم إداري بالفعل")
            return True
        
        print("📝 إنشاء مستخدم إداري جديد...")
        print("يرجى إدخال البيانات التالية:")
        
        subprocess.run([sys.executable, "manage.py", "createsuperuser"])
        print("✅ تم إنشاء المستخدم الإداري")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في إنشاء المستخدم الإداري: {e}")
        return False

def run_django_server():
    """تشغيل خادم Django"""
    print("🌐 تشغيل خادم Django...")
    try:
        subprocess.run([sys.executable, "manage.py", "runserver", "127.0.0.1:8000"])
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف خادم Django")

def run_desktop_app():
    """تشغيل تطبيق سطح المكتب"""
    print("🖥️ تشغيل تطبيق سطح المكتب...")
    try:
        subprocess.run([sys.executable, "desktop_app.py"])
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف تطبيق سطح المكتب")

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n📋 اختر طريقة التشغيل:")
    print("1. تشغيل خادم الويب (Django)")
    print("2. تشغيل تطبيق سطح المكتب (PyQt5)")
    print("3. تشغيل كلاهما")
    print("4. إعداد النظام فقط")
    print("5. خروج")
    
    choice = input("\nاختر رقم الخيار (1-5): ").strip()
    return choice

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات")
        return
    
    # تثبيت المتطلبات
    print("\n📦 هل تريد تثبيت/تحديث المتطلبات؟ (y/n): ", end="")
    if input().lower() in ['y', 'yes', 'نعم']:
        if not install_requirements():
            print("❌ فشل في تثبيت المتطلبات")
            return
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("❌ فشل في إعداد قاعدة البيانات")
        return
    
    # إنشاء مستخدم إداري
    print("\n👤 هل تريد إنشاء/فحص المستخدم الإداري؟ (y/n): ", end="")
    if input().lower() in ['y', 'yes', 'نعم']:
        create_superuser()
    
    # عرض القائمة
    while True:
        choice = show_menu()
        
        if choice == '1':
            print("\n🌐 بدء تشغيل خادم الويب...")
            print("📍 الرابط: http://127.0.0.1:8000")
            print("🛑 اضغط Ctrl+C للإيقاف")
            run_django_server()
            
        elif choice == '2':
            print("\n🖥️ بدء تشغيل تطبيق سطح المكتب...")
            run_desktop_app()
            
        elif choice == '3':
            print("\n🚀 بدء تشغيل كلا التطبيقين...")
            print("📍 خادم الويب: http://127.0.0.1:8000")
            
            # تشغيل خادم Django في thread منفصل
            django_thread = threading.Thread(target=run_django_server, daemon=True)
            django_thread.start()
            
            # انتظار قليل ثم تشغيل تطبيق سطح المكتب
            time.sleep(2)
            run_desktop_app()
            
        elif choice == '4':
            print("\n✅ تم إعداد النظام بنجاح!")
            print("يمكنك الآن تشغيل النظام باستخدام:")
            print("- خادم الويب: python manage.py runserver")
            print("- تطبيق سطح المكتب: python desktop_app.py")
            break
            
        elif choice == '5':
            print("\n👋 شكراً لاستخدام النظام المحاسبي الموحد!")
            break
            
        else:
            print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")
        print("يرجى التواصل مع الدعم الفني")
