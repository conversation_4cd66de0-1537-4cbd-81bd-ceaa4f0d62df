from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from decimal import Decimal


class Company(models.Model):
    """بيانات المؤسسة"""
    name = models.CharField(_('اسم المؤسسة'), max_length=200)
    address = models.TextField(_('العنوان'))
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)
    website = models.URLField(_('الموقع الإلكتروني'), blank=True, null=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True, null=True)
    logo = models.ImageField(_('شعار المؤسسة'), upload_to='company_logos/', blank=True, null=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('المؤسسة')
        verbose_name_plural = _('المؤسسات')
        db_table = 'companies'

    def __str__(self):
        return self.name


class Department(models.Model):
    """بيانات الدائرة"""
    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name=_('المؤسسة'))
    name = models.CharField(_('اسم الدائرة'), max_length=200)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True, null=True)
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('الدائرة')
        verbose_name_plural = _('الدوائر')
        db_table = 'accounting_departments'

    def __str__(self):
        return f"{self.name} - {self.company.name}"


class Currency(models.Model):
    """دليل العملات"""
    CURRENCY_TYPES = [
        ('local', _('محلية')),
        ('foreign', _('أجنبية')),
    ]

    name = models.CharField(_('اسم العملة'), max_length=100)
    symbol = models.CharField(_('رمز العملة'), max_length=10)
    code = models.CharField(_('كود العملة'), max_length=3, unique=True)
    subunit = models.CharField(_('أجزاء العملة'), max_length=50, blank=True, null=True)
    currency_type = models.CharField(_('نوع العملة'), max_length=10, choices=CURRENCY_TYPES)
    is_default = models.BooleanField(_('العملة الافتراضية'), default=False)
    exchange_rate = models.DecimalField(_('سعر الصرف'), max_digits=10, decimal_places=4, default=1.0000)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('العملة')
        verbose_name_plural = _('العملات')
        db_table = 'currencies'

    def __str__(self):
        return f"{self.name} ({self.symbol})"

    def save(self, *args, **kwargs):
        if self.is_default:
            # إزالة الافتراضية من العملات الأخرى
            Currency.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class FiscalPeriod(models.Model):
    """الفترة المحاسبية"""
    name = models.CharField(_('اسم الفترة'), max_length=100)
    fiscal_year = models.IntegerField(_('السنة المالية'))
    start_month = models.IntegerField(_('من شهر'), validators=[MinValueValidator(1)])
    end_month = models.IntegerField(_('إلى شهر'), validators=[MinValueValidator(1)])
    number_of_months = models.IntegerField(_('عدد الأشهر'), validators=[MinValueValidator(1)])
    is_current = models.BooleanField(_('الفترة الحالية'), default=False)
    is_closed = models.BooleanField(_('مقفلة'), default=False)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('الفترة المحاسبية')
        verbose_name_plural = _('الفترات المحاسبية')
        db_table = 'fiscal_periods'
        unique_together = ['fiscal_year', 'start_month', 'end_month']

    def __str__(self):
        return f"{self.name} - {self.fiscal_year}"

    def save(self, *args, **kwargs):
        if self.is_current:
            # إزالة الحالية من الفترات الأخرى
            FiscalPeriod.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)


class AccountType(models.Model):
    """أنواع الحسابات"""
    ACCOUNT_CATEGORIES = [
        ('assets', _('الأصول')),
        ('liabilities', _('الخصوم')),
        ('equity', _('حقوق الملكية')),
        ('revenue', _('الإيرادات')),
        ('expenses', _('المصروفات')),
    ]

    name = models.CharField(_('اسم نوع الحساب'), max_length=100, unique=True)
    category = models.CharField(_('فئة الحساب'), max_length=20, choices=ACCOUNT_CATEGORIES)
    code = models.CharField(_('رمز النوع'), max_length=10, unique=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('نوع الحساب')
        verbose_name_plural = _('أنواع الحسابات')
        db_table = 'account_types'

    def __str__(self):
        return self.name


class Account(models.Model):
    """دليل الحسابات"""
    ACCOUNT_LEVELS = [
        ('main', _('رئيسي')),
        ('sub', _('فرعي')),
    ]

    code = models.CharField(_('رمز الحساب'), max_length=20, unique=True)
    name = models.CharField(_('اسم الحساب'), max_length=200)
    account_type = models.ForeignKey(AccountType, on_delete=models.PROTECT, verbose_name=_('نوع الحساب'))
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                              verbose_name=_('الحساب الأب'))
    level = models.CharField(_('مستوى الحساب'), max_length=10, choices=ACCOUNT_LEVELS)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, verbose_name=_('العملة'))
    is_active = models.BooleanField(_('نشط'), default=True)
    opening_balance = models.DecimalField(_('الرصيد الافتتاحي'), max_digits=15, decimal_places=2, default=0.00)
    current_balance = models.DecimalField(_('الرصيد الحالي'), max_digits=15, decimal_places=2, default=0.00)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('الحساب')
        verbose_name_plural = _('الحسابات')
        db_table = 'accounts'
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class CashBox(models.Model):
    """دليل الصناديق"""
    name = models.CharField(_('اسم الصندوق'), max_length=100, unique=True)
    code = models.CharField(_('رمز الصندوق'), max_length=10, unique=True)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, verbose_name=_('العملة'))
    opening_balance = models.DecimalField(_('الرصيد الافتتاحي'), max_digits=15, decimal_places=2, default=0.00)
    current_balance = models.DecimalField(_('الرصيد الحالي'), max_digits=15, decimal_places=2, default=0.00)
    is_active = models.BooleanField(_('نشط'), default=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('الصندوق')
        verbose_name_plural = _('الصناديق')
        db_table = 'cash_boxes'

    def __str__(self):
        return self.name


class Bank(models.Model):
    """دليل المصارف"""
    name = models.CharField(_('اسم المصرف'), max_length=100, unique=True)
    code = models.CharField(_('رمز المصرف'), max_length=10, unique=True)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True, null=True)
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)
    swift_code = models.CharField(_('رمز SWIFT'), max_length=11, blank=True, null=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('المصرف')
        verbose_name_plural = _('المصارف')
        db_table = 'banks'

    def __str__(self):
        return self.name


class BankBranch(models.Model):
    """دليل فروع المصارف"""
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, verbose_name=_('المصرف'))
    name = models.CharField(_('اسم الفرع'), max_length=100)
    code = models.CharField(_('رمز الفرع'), max_length=10)
    address = models.TextField(_('العنوان'), blank=True, null=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True, null=True)
    manager = models.CharField(_('مدير الفرع'), max_length=100, blank=True, null=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فرع المصرف')
        verbose_name_plural = _('فروع المصارف')
        db_table = 'bank_branches'
        unique_together = ['bank', 'code']

    def __str__(self):
        return f"{self.name} - {self.bank.name}"


class BankAccount(models.Model):
    """دليل الحسابات البنكية"""
    ACCOUNT_TYPES = [
        ('operational', _('التشغيلية')),
        ('payroll_treasury', _('الخزينة الموحد (الرواتب)')),
        ('savings', _('توفير')),
        ('current', _('جاري')),
        ('fixed_deposit', _('وديعة ثابتة')),
    ]

    bank = models.ForeignKey(Bank, on_delete=models.PROTECT, verbose_name=_('المصرف'))
    branch = models.ForeignKey(BankBranch, on_delete=models.PROTECT, verbose_name=_('الفرع'))
    account_number = models.CharField(_('رقم الحساب'), max_length=50, unique=True)
    account_name = models.CharField(_('اسم الحساب'), max_length=200)
    account_type = models.CharField(_('نوع الحساب'), max_length=20, choices=ACCOUNT_TYPES)
    iban = models.CharField(_('رقم IBAN'), max_length=34, blank=True, null=True)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, verbose_name=_('العملة'))
    opening_balance = models.DecimalField(_('الرصيد الافتتاحي'), max_digits=15, decimal_places=2, default=0.00)
    current_balance = models.DecimalField(_('الرصيد الحالي'), max_digits=15, decimal_places=2, default=0.00)
    is_active = models.BooleanField(_('نشط'), default=True)
    is_default_payroll = models.BooleanField(_('حساب الرواتب الافتراضي'), default=False)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('الحساب البنكي')
        verbose_name_plural = _('الحسابات البنكية')
        db_table = 'bank_accounts'

    def __str__(self):
        return f"{self.account_name} - {self.account_number}"

    def save(self, *args, **kwargs):
        if self.is_default_payroll and self.account_type == 'payroll_treasury':
            # إزالة الافتراضية من حسابات الرواتب الأخرى
            BankAccount.objects.filter(
                is_default_payroll=True,
                account_type='payroll_treasury'
            ).update(is_default_payroll=False)
        super().save(*args, **kwargs)


class CostCenter(models.Model):
    """مراكز التكلفة"""
    name = models.CharField(_('اسم مركز التكلفة'), max_length=100, unique=True)
    code = models.CharField(_('رمز مركز التكلفة'), max_length=10, unique=True)
    description = models.TextField(_('الوصف'), blank=True, null=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مركز التكلفة')
        verbose_name_plural = _('مراكز التكلفة')
        db_table = 'cost_centers'

    def __str__(self):
        return f"{self.code} - {self.name}"
