# 🚀 دليل التشغيل السريع - نظام إدارة الرواتب والمحاسبة

## 📋 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل:** Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python:** 3.8 أو أحدث
- **الذاكرة:** 4 GB RAM
- **مساحة القرص:** 2 GB متاحة

### المستحسن
- **الذاكرة:** 8 GB RAM أو أكثر
- **المعالج:** Intel i5 أو AMD Ryzen 5 أو أفضل
- **الشاشة:** 1366x768 أو أعلى

## ⚡ التشغيل السريع

### 🔧 إصلاح المشاكل أولاً (إذا لزم الأمر)

#### إصلاح تلقائي شامل
1. **انقر نقراً مزدوجاً على:** `fix_system.bat` (Windows)
2. **أو شغل:** `python fix_system.py` (جميع الأنظمة)

#### تثبيت المتطلبات فقط
1. **انقر نقراً مزدوجاً على:** `install_requirements.bat` (Windows)
2. **أو شغل:** `python install_requirements.py` (جميع الأنظمة)

### 🖥️ تطبيق سطح المكتب

#### الطريقة الأولى (الأسهل)
1. **انقر نقراً مزدوجاً على:** `start_desktop.bat` (Windows)
2. **أو شغل:** `python start_desktop.py` (جميع الأنظمة)

#### الطريقة الثانية (يدوياً)
```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. إعداد قاعدة البيانات
python manage.py migrate

# 3. إنشاء مستخدم إداري
python manage.py shell -c "
from users.models import CustomUser
from django.contrib.auth.hashers import make_password
CustomUser.objects.filter(username='admin').delete()
CustomUser.objects.create(
    username='admin',
    email='<EMAIL>', 
    account_name='المدير العام',
    password=make_password('admin123'),
    is_staff=True,
    is_superuser=True,
    is_active=True
)"

# 4. تشغيل التطبيق
python desktop_app.py
```

### 🌐 واجهة الويب
```bash
# تشغيل خادم Django
python manage.py runserver

# ثم افتح المتصفح على:
# http://127.0.0.1:8000
```

## 🔑 بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **البريد الإلكتروني:** `<EMAIL>`

## 🎯 الواجهة الرئيسية

### القائمة الجانبية
- **🏠 الصفحة الرئيسية** - لوحة المعلومات والإحصائيات
- **👥 المستخدمين** - إدارة حسابات المستخدمين والصلاحيات
- **📋 البيانات والتوثيق** - إدارة البيانات الأساسية للنظام
- **👤 الموظفين** - إضافة وتعديل بيانات الموظفين
- **💰 المرتبات** - إدارة هياكل المرتبات والعلاوات
- **💵 الرواتب** - حساب ومعالجة الرواتب الشهرية
- **📊 التقارير** - إنشاء وعرض التقارير المختلفة
- **📖 دليل البرامج** - المساعدة ودليل الاستخدام

### الصفحة الرئيسية
تحتوي على:
- **بطاقات إحصائية ملونة** تعرض أهم الأرقام
- **قائمة الأنشطة الأخيرة** مع التحديثات الفورية
- **روابط سريعة** للوظائف الأكثر استخداماً

## 🛠️ حل المشاكل الشائعة

### 🚀 الحل السريع لجميع المشاكل
```bash
# تشغيل أداة الإصلاح الشاملة
python fix_system.py
# أو انقر على fix_system.bat
```

### مشكلة: "ModuleNotFoundError" أو "Import Error"
```bash
# الحل الأول: أداة التثبيت التلقائي
python install_requirements.py

# الحل الثاني: التثبيت اليدوي
pip install PyQt5 Django pandas openpyxl psycopg2-binary
```

### مشكلة: "no such table: users_customuser"
```bash
# الحل التلقائي
python fix_system.py

# الحل اليدوي
python manage.py makemigrations
python manage.py migrate
```

### مشكلة: "Invalid username or password"
```bash
# الحل التلقائي
python fix_system.py

# الحل اليدوي - إعادة إنشاء المستخدم الإداري
python manage.py shell -c "
from users.models import CustomUser
from django.contrib.auth.hashers import make_password
CustomUser.objects.filter(username='admin').delete()
CustomUser.objects.create(
    username='admin',
    email='<EMAIL>',
    account_name='المدير العام',
    password=make_password('admin123'),
    is_staff=True,
    is_superuser=True,
    is_active=True
)"
```

### مشكلة: التطبيق لا يفتح
1. **استخدم أداة الإصلاح:**
   ```bash
   python fix_system.py
   ```

2. **تأكد من إصدار Python:**
   ```bash
   python --version
   # يجب أن يكون 3.8 أو أحدث
   ```

3. **فحص رسائل الخطأ:**
   ```bash
   python desktop_app.py
   # اقرأ رسائل الخطأ بعناية
   ```

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. **تأكد من تثبيت جميع المتطلبات**
2. **راجع رسائل الخطأ في Terminal/Command Prompt**
3. **تأكد من صحة بيانات تسجيل الدخول**
4. **أعد تشغيل التطبيق**

### معلومات مفيدة للدعم:
- إصدار Python: `python --version`
- إصدار النظام: Windows/macOS/Linux
- رسالة الخطأ الكاملة
- الخطوات التي أدت للمشكلة

## 🎉 نصائح للاستخدام الأمثل

1. **استخدم المشغل التلقائي** `start_desktop.py` لتجنب المشاكل
2. **احفظ بياناتك بانتظام** من خلال النسخ الاحتياطية
3. **استخدم أسماء مستخدمين وكلمات مرور قوية** في البيئة الإنتاجية
4. **راجع التقارير بانتظام** لمتابعة أداء النظام
5. **حدث النظام بانتظام** للحصول على أحدث الميزات

---

**🏢 نظام إدارة الرواتب والمحاسبة - دليل التشغيل السريع**
**© 2024 - جميع الحقوق محفوظة**
