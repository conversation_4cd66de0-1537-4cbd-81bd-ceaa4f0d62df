/* Main Styles */
@font-face {
    font-family: 'Cairo';
    src: url('../fonts/Cairo-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}

:root {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f1c40f;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --text-color: #333;
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

/* General Styles */
body {
    font-family: 'Cairo', sans-serif;
    font-size: 12pt;
    font-weight: bold;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--light-color);
}

/* Layout */
.wrapper {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
}

.main-content {
    flex: 1;
    padding: 2rem;
    background-color: white;
}

/* Navigation */
.nav-link {
    color: white;
    padding: 0.5rem 1rem;
    margin: 0.2rem 0;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.nav-link:hover {
    background-color: var(--secondary-color);
    color: white;
}

.nav-link.active {
    background-color: var(--accent-color);
}

/* Cards */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
    border-radius: 8px 8px 0 0;
}

/* Forms */
.form-control {
    border-radius: 4px;
    border: 1px solid #ddd;
    padding: 0.5rem;
    margin-bottom: 1rem;
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Buttons */
.btn {
    border-radius: 4px;
    padding: 0.5rem 1.5rem;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: white;
}

.table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: bold;
    padding: 1rem;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

/* Utilities */
.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

/* Dashboard Widgets */
.widget {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.widget-title {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.widget-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-color);
}

/* Print Styles */
@media print {
    .sidebar {
        display: none;
    }

    .main-content {
        margin: 0;
        padding: 0;
    }

    .no-print {
        display: none;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .wrapper {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        margin-bottom: 1rem;
    }

    .main-content {
        padding: 1rem;
    }
}