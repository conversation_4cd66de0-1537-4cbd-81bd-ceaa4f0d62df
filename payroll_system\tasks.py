"""
مهام Celery للنظام المحاسبي الموحد
"""

import os
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path

from celery import shared_task
from django.conf import settings
from django.core.management import call_command
from django.core.mail import send_mail
from django.utils import timezone

logger = logging.getLogger(__name__)


@shared_task
def create_daily_backup():
    """إنشاء نسخة احتياطية يومية"""
    try:
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_dir = Path(settings.BASE_DIR) / 'backups'
        backup_dir.mkdir(exist_ok=True)
        
        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'payroll_backup_{timestamp}.json'
        backup_path = backup_dir / backup_filename
        
        # إنشاء النسخة الاحتياطية
        with open(backup_path, 'w', encoding='utf-8') as f:
            call_command('dumpdata', '--natural-foreign', '--natural-primary', 
                        '--exclude=contenttypes', '--exclude=auth.permission', 
                        stdout=f)
        
        logger.info(f'تم إنشاء النسخة الاحتياطية: {backup_filename}')
        
        # حذف النسخ القديمة (أكثر من 30 يوم)
        cleanup_old_backups(backup_dir, days=30)
        
        return f'تم إنشاء النسخة الاحتياطية بنجاح: {backup_filename}'
        
    except Exception as e:
        logger.error(f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}')
        return f'فشل في إنشاء النسخة الاحتياطية: {str(e)}'


@shared_task
def cleanup_temp_files():
    """تنظيف الملفات المؤقتة"""
    try:
        cleaned_files = 0
        
        # تنظيف ملفات التقارير المؤقتة
        reports_dir = Path(settings.MEDIA_ROOT) / 'reports'
        if reports_dir.exists():
            cutoff_date = timezone.now() - timedelta(days=7)
            
            for file_path in reports_dir.glob('*'):
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    file_time = timezone.make_aware(file_time)
                    
                    if file_time < cutoff_date:
                        file_path.unlink()
                        cleaned_files += 1
        
        # تنظيف ملفات السجلات القديمة
        logs_dir = Path(settings.BASE_DIR) / 'logs'
        if logs_dir.exists():
            cutoff_date = timezone.now() - timedelta(days=30)
            
            for file_path in logs_dir.glob('*.log.*'):
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    file_time = timezone.make_aware(file_time)
                    
                    if file_time < cutoff_date:
                        file_path.unlink()
                        cleaned_files += 1
        
        logger.info(f'تم تنظيف {cleaned_files} ملف مؤقت')
        return f'تم تنظيف {cleaned_files} ملف مؤقت'
        
    except Exception as e:
        logger.error(f'خطأ في تنظيف الملفات المؤقتة: {str(e)}')
        return f'فشل في تنظيف الملفات المؤقتة: {str(e)}'


@shared_task
def send_notification_email(subject, message, recipient_list):
    """إرسال بريد إلكتروني للإشعارات"""
    try:
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=recipient_list,
            fail_silently=False,
        )
        
        logger.info(f'تم إرسال بريد إلكتروني إلى: {recipient_list}')
        return f'تم إرسال البريد الإلكتروني بنجاح'
        
    except Exception as e:
        logger.error(f'خطأ في إرسال البريد الإلكتروني: {str(e)}')
        return f'فشل في إرسال البريد الإلكتروني: {str(e)}'


@shared_task
def calculate_payroll_batch(employee_ids, period_id):
    """حساب الرواتب لمجموعة من الموظفين"""
    try:
        from payroll.models import Payroll, PayrollPeriod
        from employees.models import Employee
        
        period = PayrollPeriod.objects.get(id=period_id)
        employees = Employee.objects.filter(id__in=employee_ids, status='active')
        
        calculated_count = 0
        errors = []
        
        for employee in employees:
            try:
                # إنشاء أو تحديث مسير الراتب
                payroll, created = Payroll.objects.get_or_create(
                    employee=employee,
                    period=period,
                    defaults={'status': 'draft'}
                )
                
                # حساب الاستقطاعات التلقائية
                payroll.calculate_automatic_deductions()
                
                # حساب الإجماليات
                payroll.calculate_totals()
                
                # تحديث الحالة
                payroll.status = 'calculated'
                payroll.calculated_at = timezone.now()
                payroll.save()
                
                calculated_count += 1
                
            except Exception as e:
                error_msg = f'خطأ في حساب راتب الموظف {employee.full_name}: {str(e)}'
                errors.append(error_msg)
                logger.error(error_msg)
        
        result = f'تم حساب {calculated_count} راتب من أصل {len(employee_ids)}'
        if errors:
            result += f'\nأخطاء: {len(errors)}'
        
        logger.info(result)
        return result
        
    except Exception as e:
        logger.error(f'خطأ في حساب الرواتب الجماعي: {str(e)}')
        return f'فشل في حساب الرواتب الجماعي: {str(e)}'


@shared_task
def generate_report(template_id, parameters, user_id):
    """إنشاء تقرير في الخلفية"""
    try:
        from reports.models import ReportTemplate, ReportExecution
        from users.models import CustomUser
        
        template = ReportTemplate.objects.get(id=template_id)
        user = CustomUser.objects.get(id=user_id)
        
        # إنشاء سجل تنفيذ التقرير
        execution = ReportExecution.objects.create(
            template=template,
            executed_by=user,
            parameters_used=parameters,
            status='running'
        )
        
        start_time = timezone.now()
        
        try:
            # هنا يتم تنفيذ التقرير الفعلي
            # يمكن إضافة منطق إنشاء التقرير حسب النوع
            
            # محاكاة إنشاء التقرير
            import time
            time.sleep(5)  # محاكاة وقت المعالجة
            
            # تحديث حالة التنفيذ
            execution.status = 'completed'
            execution.execution_time = timezone.now() - start_time
            execution.save()
            
            logger.info(f'تم إنشاء التقرير: {template.name}')
            return f'تم إنشاء التقرير بنجاح: {template.name}'
            
        except Exception as e:
            execution.status = 'failed'
            execution.error_message = str(e)
            execution.execution_time = timezone.now() - start_time
            execution.save()
            raise e
        
    except Exception as e:
        logger.error(f'خطأ في إنشاء التقرير: {str(e)}')
        return f'فشل في إنشاء التقرير: {str(e)}'


def cleanup_old_backups(backup_dir, days=30):
    """حذف النسخ الاحتياطية القديمة"""
    cutoff_date = datetime.now() - timedelta(days=days)
    
    for backup_file in backup_dir.glob('payroll_backup_*.json'):
        if backup_file.is_file():
            file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
            
            if file_time < cutoff_date:
                backup_file.unlink()
                logger.info(f'تم حذف النسخة الاحتياطية القديمة: {backup_file.name}')


@shared_task
def sync_employee_data():
    """مزامنة بيانات الموظفين مع أنظمة خارجية"""
    try:
        # هنا يمكن إضافة منطق المزامنة مع أنظمة الحضور والانصراف
        # أو أي أنظمة خارجية أخرى
        
        logger.info('تم تنفيذ مزامنة بيانات الموظفين')
        return 'تم تنفيذ مزامنة بيانات الموظفين بنجاح'
        
    except Exception as e:
        logger.error(f'خطأ في مزامنة بيانات الموظفين: {str(e)}')
        return f'فشل في مزامنة بيانات الموظفين: {str(e)}'


@shared_task
def archive_old_data():
    """أرشفة البيانات القديمة"""
    try:
        from payroll.models import Payroll
        from reports.models import ReportExecution
        
        # أرشفة مسيرات الرواتب القديمة (أكثر من سنتين)
        cutoff_date = timezone.now() - timedelta(days=730)
        old_payrolls = Payroll.objects.filter(created_at__lt=cutoff_date, status='paid')
        
        archived_count = 0
        for payroll in old_payrolls:
            # هنا يمكن نقل البيانات إلى جدول أرشيف أو ملف منفصل
            archived_count += 1
        
        # أرشفة تنفيذات التقارير القديمة
        old_executions = ReportExecution.objects.filter(created_at__lt=cutoff_date)
        old_executions.delete()
        
        logger.info(f'تم أرشفة {archived_count} مسير راتب')
        return f'تم أرشفة {archived_count} مسير راتب'
        
    except Exception as e:
        logger.error(f'خطأ في أرشفة البيانات: {str(e)}')
        return f'فشل في أرشفة البيانات: {str(e)}'
