# Generated by Django 5.2.2 on 2025-06-06 09:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("employees", "0002_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Department",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "department_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الدائرة"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الدائرة")),
                (
                    "address",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="العنوان"
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True, max_length=20, verbose_name="رقم الهاتف"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="البريد الإلكتروني"
                    ),
                ),
            ],
            options={
                "verbose_name": "الدائرة",
                "verbose_name_plural": "الدوائر",
            },
        ),
        migrations.RemoveField(
            model_name="basesalary",
            name="employee",
        ),
        migrations.RemoveField(
            model_name="deduction",
            name="employee",
        ),
        migrations.AlterUniqueTogether(
            name="salarysheet",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="salarysheet",
            name="employee",
        ),
        migrations.AlterField(
            model_name="employee",
            name="department",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="employees.department",
                verbose_name="الدائرة",
            ),
        ),
        migrations.DeleteModel(
            name="Allowance",
        ),
        migrations.DeleteModel(
            name="BaseSalary",
        ),
        migrations.DeleteModel(
            name="Deduction",
        ),
        migrations.DeleteModel(
            name="SalarySheet",
        ),
    ]
