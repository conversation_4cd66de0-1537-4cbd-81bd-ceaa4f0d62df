from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _

class CoreConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'core'
    verbose_name = _('النظام الأساسي')

    def ready(self):
        try:
            # تأكد من أن الجداول موجودة قبل محاولة استخدامها
            from django.db import connection
            if 'core_systemlog' in connection.introspection.table_names():
                from .activity_models import SystemLog
                SystemLog.info('تم بدء تشغيل النظام بنجاح')
        except Exception:
            # تجاهل الأخطاء أثناء بدء التشغيل
            pass

        # استيراد إشارات التطبيق
        try:
            import core.signals
        except ImportError:
            pass

        # إنشاء المجلدات المطلوبة
        try:
            import os
            from django.conf import settings
            
            directories = [
                settings.MEDIA_ROOT,
                settings.STATIC_ROOT,
                settings.BACKUP_DIR,
                settings.TEMP_DIR,
                os.path.join(settings.MEDIA_ROOT, 'organization_logos'),
                os.path.join(settings.MEDIA_ROOT, 'employee_photos'),
                os.path.join(settings.BACKUP_DIR, 'daily'),
                os.path.join(settings.BACKUP_DIR, 'weekly'),
                os.path.join(settings.BACKUP_DIR, 'monthly'),
            ]

            for directory in directories:
                os.makedirs(directory, exist_ok=True)
        except Exception:
            pass
