from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from core.models import Organization, Currency
from core.activity_models import SystemLog

User = get_user_model()

class Command(BaseCommand):
    help = 'تهيئة النظام وإنشاء البيانات الأساسية'

    def handle(self, *args, **options):
        try:
            self.stdout.write('بدء تهيئة النظام...')

            # إنشاء المشرف الرئيسي
            if not User.objects.filter(username='admin').exists():
                admin = User.objects.create_superuser(
                    username='admin',
                    password='admin123',
                    email='<EMAIL>',
                    first_name='مدير',
                    last_name='النظام'
                )
                self.stdout.write(self.style.SUCCESS('تم إنشاء حساب المشرف الرئيسي'))

            # إنشاء بيانات المؤسسة
            organization, created = Organization.objects.get_or_create(
                pk=1,
                defaults={
                    'name': 'المحاسب الشامل',
                    'email': '<EMAIL>',
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS('تم إنشاء بيانات المؤسسة'))

            # إنشاء العملات الأساسية
            currencies = [
                {
                    'currency_number': '001',
                    'name': 'دينار عراقي',
                    'symbol': 'د.ع',
                    'fraction_name': 'فلس',
                    'currency_type': 'local'
                },
                {
                    'currency_number': '002',
                    'name': 'دولار أمريكي',
                    'symbol': '$',
                    'fraction_name': 'سنت',
                    'currency_type': 'foreign'
                }
            ]

            for currency_data in currencies:
                currency, created = Currency.objects.get_or_create(
                    currency_number=currency_data['currency_number'],
                    defaults=currency_data
                )
                if created:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'تم إنشاء العملة: {currency.name}'
                        )
                    )

            # إنشاء المجلدات المطلوبة
            import os
            from django.conf import settings

            folders = [
                settings.MEDIA_ROOT,
                settings.STATIC_ROOT,
                settings.BACKUP_DIR,
                settings.TEMP_DIR,
                os.path.join(settings.MEDIA_ROOT, 'organization_logos'),
                os.path.join(settings.MEDIA_ROOT, 'employee_photos'),
                os.path.join(settings.BACKUP_DIR, 'daily'),
                os.path.join(settings.BACKUP_DIR, 'weekly'),
                os.path.join(settings.BACKUP_DIR, 'monthly'),
            ]

            for folder in folders:
                if not os.path.exists(folder):
                    os.makedirs(folder)
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'تم إنشاء المجلد: {folder}'
                        )
                    )

            # تسجيل نجاح التهيئة
            SystemLog.info('تم تهيئة النظام بنجاح')
            self.stdout.write(self.style.SUCCESS('تم تهيئة النظام بنجاح'))

        except Exception as e:
            # تسجيل الخطأ
            SystemLog.error('فشل في تهيئة النظام', str(e))
            self.stdout.write(
                self.style.ERROR(
                    f'حدث خطأ أثناء تهيئة النظام: {str(e)}'
                )
            )
            raise