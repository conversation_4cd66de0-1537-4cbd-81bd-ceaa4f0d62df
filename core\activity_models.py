from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _

class Activity(models.Model):
    """نموذج لتتبع نشاطات المستخدمين في النظام"""
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name=_('المستخدم')
    )
    action = models.CharField(
        _('النشاط'),
        max_length=255,
        help_text=_('وصف النشاط الذي قام به المستخدم')
    )
    details = models.TextField(
        _('التفاصيل'),
        blank=True,
        help_text=_('تفاصيل إضافية عن النشاط')
    )
    ip_address = models.GenericIPAddressField(
        _('عنوان IP'),
        null=True,
        blank=True,
        help_text=_('عنوان IP للمستخدم عند تنفيذ النشاط')
    )
    user_agent = models.TextField(
        _('متصفح المستخدم'),
        blank=True,
        help_text=_('معلومات عن متصفح المستخدم')
    )
    created_at = models.DateTimeField(
        _('تاريخ النشاط'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('النشاط')
        verbose_name_plural = _('النشاطات')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['-created_at']),
            models.Index(fields=['user', '-created_at']),
        ]

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.action}"

    @classmethod
    def log_activity(cls, user, action, details='', request=None):
        """تسجيل نشاط جديد"""
        activity = cls(
            user=user,
            action=action,
            details=details
        )
        
        if request:
            activity.ip_address = request.META.get('REMOTE_ADDR')
            activity.user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        activity.save()
        return activity

class SystemLog(models.Model):
    """نموذج لتسجيل أحداث النظام المهمة"""
    
    LEVEL_CHOICES = [
        ('info', _('معلومات')),
        ('warning', _('تحذير')),
        ('error', _('خطأ')),
        ('critical', _('خطأ حرج')),
    ]

    level = models.CharField(
        _('المستوى'),
        max_length=10,
        choices=LEVEL_CHOICES,
        default='info'
    )
    message = models.TextField(
        _('الرسالة'),
        help_text=_('وصف الحدث')
    )
    traceback = models.TextField(
        _('تتبع الخطأ'),
        blank=True,
        help_text=_('تفاصيل تقنية عن الخطأ إن وجدت')
    )
    created_at = models.DateTimeField(
        _('تاريخ الحدث'),
        auto_now_add=True
    )

    class Meta:
        verbose_name = _('سجل النظام')
        verbose_name_plural = _('سجلات النظام')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['-created_at']),
            models.Index(fields=['level', '-created_at']),
        ]

    def __str__(self):
        return f"[{self.get_level_display()}] {self.message[:100]}"

    @classmethod
    def log(cls, message, level='info', traceback=''):
        """تسجيل حدث جديد في سجل النظام"""
        return cls.objects.create(
            level=level,
            message=message,
            traceback=traceback
        )

    @classmethod
    def info(cls, message):
        """تسجيل معلومة في سجل النظام"""
        return cls.log(message, level='info')

    @classmethod
    def warning(cls, message):
        """تسجيل تحذير في سجل النظام"""
        return cls.log(message, level='warning')

    @classmethod
    def error(cls, message, traceback=''):
        """تسجيل خطأ في سجل النظام"""
        return cls.log(message, level='error', traceback=traceback)

    @classmethod
    def critical(cls, message, traceback=''):
        """تسجيل خطأ حرج في سجل النظام"""
        return cls.log(message, level='critical', traceback=traceback)