#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النظام المحاسبي الموحد - رواتب الموظفين
واجهة سطح المكتب الرئيسية
"""

import sys
import os
import django
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QMenuBar, QStatusBar, QToolBar,
                             QAction, QTabWidget, QLabel, QPushButton,
                             QMessageBox, QSplashScreen, QProgressBar,
                             QListWidget, QListWidgetItem, QStackedWidget,
                             QFrame, QScrollArea, QGridLayout, QSizePolicy,
                             QSpacerItem)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize
from PyQt5.QtGui import QIcon, QPixmap, <PERSON>Font, Q<PERSON>alette, QColor

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'payroll_system.settings')
django.setup()

from django.contrib.auth import authenticate
from users.models import CustomUser
from desktop_styles import (get_main_stylesheet, get_card_stylesheet,
                           get_activity_stylesheet, get_page_title_stylesheet,
                           get_page_description_stylesheet, get_sidebar_header_stylesheet,
                           get_user_info_stylesheet, get_development_notice_stylesheet,
                           get_content_frame_stylesheet, get_stat_value_stylesheet,
                           get_stat_title_stylesheet, get_stat_icon_stylesheet)


class LoginDialog(QWidget):
    """نافذة تسجيل الدخول"""
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('تسجيل الدخول - النظام المحاسبي الموحد')
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # تخطيط رئيسي
        layout = QVBoxLayout()
        
        # شعار النظام
        logo_label = QLabel()
        logo_label.setText('النظام المحاسبي الموحد\nرواتب الموظفين')
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(logo_label)
        
        # حقول تسجيل الدخول
        from PyQt5.QtWidgets import QLineEdit, QFormLayout
        
        form_layout = QFormLayout()
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText('اسم المستخدم')
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin: 5px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('كلمة المرور')
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.username_input.styleSheet())
        
        form_layout.addRow('اسم المستخدم:', self.username_input)
        form_layout.addRow('كلمة المرور:', self.password_input)
        
        layout.addLayout(form_layout)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton('تسجيل الدخول')
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.login_button.clicked.connect(self.login)
        
        self.cancel_button = QPushButton('إلغاء')
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.cancel_button.clicked.connect(self.close)
        
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # ربط Enter بتسجيل الدخول
        self.password_input.returnPressed.connect(self.login)
        
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, 'خطأ', 'يرجى إدخال اسم المستخدم وكلمة المرور')
            return
            
        try:
            user = authenticate(username=username, password=password)
            if user and user.is_active:
                self.user = user
                self.accept()
            else:
                QMessageBox.warning(self, 'خطأ', 'اسم المستخدم أو كلمة المرور غير صحيحة')
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تسجيل الدخول:\n{str(e)}')
    
    def accept(self):
        """قبول تسجيل الدخول"""
        self.close()
        
    def get_user(self):
        """الحصول على المستخدم المسجل"""
        return getattr(self, 'user', None)


class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.current_page = 'dashboard'
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle('نظام إدارة الرواتب والمحاسبة')
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)

        # إعداد النافذة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # تخطيط رئيسي أفقي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        central_widget.setLayout(main_layout)

        # إنشاء القائمة الجانبية
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)

        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        main_layout.addWidget(self.main_content)

        # إنشاء شريط الحالة
        self.create_statusbar()

        # تطبيق الأنماط
        self.apply_styles()

        # عرض الصفحة الرئيسية
        self.show_dashboard()
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(280)
        self.sidebar.setFrameStyle(QFrame.StyledPanel)

        sidebar_layout = QVBoxLayout()
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        self.sidebar.setLayout(sidebar_layout)

        # شعار النظام
        header = QWidget()
        header.setFixedHeight(80)
        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(20, 15, 20, 15)

        title_label = QLabel('نظام إدارة الرواتب والمحاسبة')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        header_layout.addWidget(title_label)
        header.setLayout(header_layout)
        sidebar_layout.addWidget(header)

        # قائمة التنقل
        self.nav_list = QListWidget()
        self.nav_list.setFrameStyle(QFrame.NoFrame)

        # إضافة عناصر القائمة
        nav_items = [
            ('الصفحة الرئيسية', 'dashboard', '🏠'),
            ('المستخدمين', 'users', '👥'),
            ('البيانات والتوثيق', 'data_docs', '📋'),
            ('الموظفين', 'employees', '👤'),
            ('المرتبات', 'salaries', '💰'),
            ('الرواتب', 'payroll', '💵'),
            ('التقارير', 'reports', '📊'),
            ('دليل البرامج', 'programs', '📖')
        ]

        for text, key, icon in nav_items:
            item = QListWidgetItem(f'{icon}  {text}')
            item.setData(Qt.UserRole, key)
            item.setSizeHint(QSize(260, 50))
            self.nav_list.addItem(item)

        self.nav_list.itemClicked.connect(self.on_nav_item_clicked)
        self.nav_list.setCurrentRow(0)  # تحديد العنصر الأول

        sidebar_layout.addWidget(self.nav_list)

        # مساحة فارغة في الأسفل
        sidebar_layout.addStretch()

        # معلومات المستخدم
        user_info = QWidget()
        user_info.setFixedHeight(60)
        user_info_layout = QVBoxLayout()
        user_info_layout.setContentsMargins(20, 10, 20, 10)

        user_label = QLabel(f'مرحباً، {self.user.account_name}')
        user_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                background: transparent;
            }
        """)
        user_info_layout.addWidget(user_label)
        user_info.setLayout(user_info_layout)
        sidebar_layout.addWidget(user_info)

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        self.main_content = QStackedWidget()

        # إنشاء الصفحات المختلفة
        self.pages = {}

        # الصفحة الرئيسية
        self.pages['dashboard'] = self.create_dashboard_page()
        self.main_content.addWidget(self.pages['dashboard'])

        # صفحة المستخدمين
        self.pages['users'] = self.create_users_page()
        self.main_content.addWidget(self.pages['users'])

        # صفحة البيانات والتوثيق
        self.pages['data_docs'] = self.create_data_docs_page()
        self.main_content.addWidget(self.pages['data_docs'])

        # صفحة الموظفين
        self.pages['employees'] = self.create_employees_page()
        self.main_content.addWidget(self.pages['employees'])

        # صفحة المرتبات
        self.pages['salaries'] = self.create_salaries_page()
        self.main_content.addWidget(self.pages['salaries'])

        # صفحة الرواتب
        self.pages['payroll'] = self.create_payroll_page()
        self.main_content.addWidget(self.pages['payroll'])

        # صفحة التقارير
        self.pages['reports'] = self.create_reports_page()
        self.main_content.addWidget(self.pages['reports'])

        # صفحة دليل البرامج
        self.pages['programs'] = self.create_programs_page()
        self.main_content.addWidget(self.pages['programs'])

    def on_nav_item_clicked(self, item):
        """معالج النقر على عنصر في القائمة الجانبية"""
        page_key = item.data(Qt.UserRole)
        self.current_page = page_key

        # تغيير الصفحة المعروضة
        if page_key in self.pages:
            page_index = list(self.pages.keys()).index(page_key)
            self.main_content.setCurrentIndex(page_index)

            # تحديث شريط الحالة
            page_names = {
                'dashboard': 'الصفحة الرئيسية',
                'users': 'إدارة المستخدمين',
                'data_docs': 'البيانات والتوثيق',
                'employees': 'إدارة الموظفين',
                'salaries': 'إدارة المرتبات',
                'payroll': 'إدارة الرواتب',
                'reports': 'التقارير والإحصائيات',
                'programs': 'دليل البرامج'
            }

            status_text = f'مرحباً {self.user.account_name} - {page_names.get(page_key, "صفحة غير معروفة")}'
            self.statusBar().showMessage(status_text)

    def create_dashboard_page(self):
        """إنشاء الصفحة الرئيسية"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # عنوان الصفحة
        title = QLabel('الصفحة الرئيسية')
        title.setStyleSheet(get_page_title_stylesheet())
        layout.addWidget(title)

        # بطاقات الإحصائيات
        stats_layout = QGridLayout()

        # بطاقة إجمالي الموظفين
        employees_card = self.create_stat_card('إجمالي الموظفين', '150', '#3498db', '👥')
        stats_layout.addWidget(employees_card, 0, 0)

        # بطاقة الرواتب الشهرية
        salary_card = self.create_stat_card('الرواتب الشهرية', '2,500,000 ريال', '#27ae60', '💰')
        stats_layout.addWidget(salary_card, 0, 1)

        # بطاقة الإجازات المعلقة
        leave_card = self.create_stat_card('الإجازات المعلقة', '25', '#e74c3c', '📅')
        stats_layout.addWidget(leave_card, 0, 2)

        # بطاقة التقارير الجديدة
        reports_card = self.create_stat_card('التقارير الجديدة', '8', '#f39c12', '📊')
        stats_layout.addWidget(reports_card, 0, 3)

        layout.addLayout(stats_layout)

        # الأنشطة الأخيرة
        activities_frame = QFrame()
        activities_frame.setFrameStyle(QFrame.StyledPanel)
        activities_layout = QVBoxLayout()
        activities_layout.setContentsMargins(20, 20, 20, 20)

        activities_title = QLabel('الأنشطة الأخيرة')
        activities_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: #14171a;
                margin-bottom: 15px;
                background: transparent;
            }
        """)
        activities_layout.addWidget(activities_title)

        # قائمة الأنشطة
        activities = [
            '✅ تم إضافة موظف جديد: أحمد محمد',
            '💰 تم معالجة راتب شهر ديسمبر',
            '📊 تم إنشاء تقرير الحضور والانصراف',
            '🔄 تم تحديث بيانات الموظف: فاطمة علي',
            '📋 تم إنشاء طلب إجازة جديد'
        ]

        for activity in activities:
            activity_label = QLabel(activity)
            activity_label.setStyleSheet(get_activity_stylesheet())
            activities_layout.addWidget(activity_label)

        activities_frame.setLayout(activities_layout)
        layout.addWidget(activities_frame)

        layout.addStretch()
        page.setLayout(layout)
        return page

    def create_stat_card(self, title, value, color, icon):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setFixedHeight(120)

        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(20, 15, 20, 15)

        # الأيقونة والقيمة
        top_layout = QHBoxLayout()

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(get_stat_icon_stylesheet(color))

        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignRight)
        value_label.setStyleSheet(get_stat_value_stylesheet(color))

        top_layout.addWidget(icon_label)
        top_layout.addStretch()
        top_layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(get_stat_title_stylesheet())

        card_layout.addLayout(top_layout)
        card_layout.addWidget(title_label)
        card_layout.addStretch()

        card.setLayout(card_layout)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 12px;
                border-left: 4px solid {color};
                padding: 0px;
            }}
            QFrame:hover {{
                border-color: {color};
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }}
        """)

        return card

    def create_users_page(self):
        """إنشاء صفحة المستخدمين"""
        return self.create_simple_page('إدارة المستخدمين', 'إدارة حسابات المستخدمين وصلاحياتهم')

    def create_data_docs_page(self):
        """إنشاء صفحة البيانات والتوثيق"""
        return self.create_simple_page('البيانات والتوثيق', 'إدارة البيانات الأساسية ووثائق النظام')

    def create_employees_page(self):
        """إنشاء صفحة الموظفين"""
        return self.create_simple_page('إدارة الموظفين', 'إضافة وتعديل وإدارة بيانات الموظفين')

    def create_salaries_page(self):
        """إنشاء صفحة المرتبات"""
        return self.create_simple_page('إدارة المرتبات', 'تحديد وإدارة هياكل المرتبات والعلاوات')

    def create_payroll_page(self):
        """إنشاء صفحة الرواتب"""
        return self.create_simple_page('إدارة الرواتب', 'معالجة وحساب رواتب الموظفين الشهرية')

    def create_reports_page(self):
        """إنشاء صفحة التقارير"""
        return self.create_simple_page('التقارير والإحصائيات', 'إنشاء وعرض التقارير المالية والإدارية')

    def create_programs_page(self):
        """إنشاء صفحة دليل البرامج"""
        return self.create_simple_page('دليل البرامج', 'دليل استخدام النظام والمساعدة')

    def create_simple_page(self, title, description):
        """إنشاء صفحة بسيطة"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # عنوان الصفحة
        title_label = QLabel(title)
        title_label.setStyleSheet(get_page_title_stylesheet())
        layout.addWidget(title_label)

        # وصف الصفحة
        desc_label = QLabel(description)
        desc_label.setStyleSheet(get_page_description_stylesheet())
        layout.addWidget(desc_label)

        # محتوى الصفحة
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.StyledPanel)
        content_frame.setStyleSheet(get_content_frame_stylesheet())

        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)

        # رسالة قيد التطوير
        dev_label = QLabel('🚧 هذه الصفحة قيد التطوير')
        dev_label.setAlignment(Qt.AlignCenter)
        dev_label.setStyleSheet(get_development_notice_stylesheet())
        content_layout.addWidget(dev_label)

        # أزرار العمليات الأساسية
        buttons_layout = QHBoxLayout()

        add_btn = QPushButton('إضافة جديد')
        add_btn.setStyleSheet(self.get_button_style('#28a745'))

        edit_btn = QPushButton('تعديل')
        edit_btn.setStyleSheet(self.get_button_style('#007bff'))

        delete_btn = QPushButton('حذف')
        delete_btn.setStyleSheet(self.get_button_style('#dc3545'))

        report_btn = QPushButton('تقرير')
        report_btn.setStyleSheet(self.get_button_style('#6f42c1'))

        buttons_layout.addWidget(add_btn)
        buttons_layout.addWidget(edit_btn)
        buttons_layout.addWidget(delete_btn)
        buttons_layout.addWidget(report_btn)
        buttons_layout.addStretch()

        content_layout.addLayout(buttons_layout)
        content_frame.setLayout(content_layout)
        layout.addWidget(content_frame)

        layout.addStretch()
        page.setLayout(layout)
        return page

    def get_button_style(self, color):
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                margin: 5px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
            QPushButton:pressed {{
                background-color: {color};
                opacity: 0.6;
            }}
        """

    def show_dashboard(self):
        """عرض الصفحة الرئيسية"""
        self.main_content.setCurrentWidget(self.pages['dashboard'])
        self.nav_list.setCurrentRow(0)


        
    def create_statusbar(self):
        """إنشاء شريط الحالة"""
        statusbar = self.statusBar()
        statusbar.showMessage(f'مرحباً {self.user.account_name} - النظام المحاسبي الموحد')
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(get_main_stylesheet())
        
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(self, 'حول البرنامج', 
                         'النظام المحاسبي الموحد - رواتب الموظفين\n'
                         'الإصدار 1.0\n'
                         'تطوير: فريق التطوير\n'
                         '© 2024 جميع الحقوق محفوظة')


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setApplicationName('النظام المحاسبي الموحد')
    app.setApplicationVersion('1.0')
    
    # تعيين الخط العربي
    font = QFont('Arial', 10)
    app.setFont(font)
    
    # عرض شاشة البداية
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    splash = QSplashScreen(splash_pix)
    splash.show()
    splash.showMessage('جاري تحميل النظام المحاسبي الموحد...', Qt.AlignCenter | Qt.AlignBottom)
    
    app.processEvents()
    
    # محاكاة تحميل
    import time
    time.sleep(2)
    
    splash.close()
    
    # عرض نافذة تسجيل الدخول
    login_dialog = LoginDialog()
    login_dialog.show()
    
    # انتظار تسجيل الدخول
    app.exec_()
    
    user = login_dialog.get_user()
    if user:
        # عرض النافذة الرئيسية
        main_window = MainWindow(user)
        main_window.show()
        
        sys.exit(app.exec_())


if __name__ == '__main__':
    main()
