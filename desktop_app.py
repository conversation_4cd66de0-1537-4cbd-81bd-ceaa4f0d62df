#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النظام المحاسبي الموحد - رواتب الموظفين
واجهة سطح المكتب الرئيسية
"""

import sys
import os
import django
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QMenuBar, QStatusBar, QToolBar, 
                             QAction, QTabWidget, QLabel, QPushButton, 
                             QMessageBox, QSplashScreen, QProgressBar)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QFont

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'payroll_system.settings')
django.setup()

from django.contrib.auth import authenticate
from users.models import CustomUser


class LoginDialog(QWidget):
    """نافذة تسجيل الدخول"""
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('تسجيل الدخول - النظام المحاسبي الموحد')
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # تخطيط رئيسي
        layout = QVBoxLayout()
        
        # شعار النظام
        logo_label = QLabel()
        logo_label.setText('النظام المحاسبي الموحد\nرواتب الموظفين')
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(logo_label)
        
        # حقول تسجيل الدخول
        from PyQt5.QtWidgets import QLineEdit, QFormLayout
        
        form_layout = QFormLayout()
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText('اسم المستخدم')
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin: 5px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('كلمة المرور')
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.username_input.styleSheet())
        
        form_layout.addRow('اسم المستخدم:', self.username_input)
        form_layout.addRow('كلمة المرور:', self.password_input)
        
        layout.addLayout(form_layout)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton('تسجيل الدخول')
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.login_button.clicked.connect(self.login)
        
        self.cancel_button = QPushButton('إلغاء')
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.cancel_button.clicked.connect(self.close)
        
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # ربط Enter بتسجيل الدخول
        self.password_input.returnPressed.connect(self.login)
        
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, 'خطأ', 'يرجى إدخال اسم المستخدم وكلمة المرور')
            return
            
        try:
            user = authenticate(username=username, password=password)
            if user and user.is_active:
                self.user = user
                self.accept()
            else:
                QMessageBox.warning(self, 'خطأ', 'اسم المستخدم أو كلمة المرور غير صحيحة')
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تسجيل الدخول:\n{str(e)}')
    
    def accept(self):
        """قبول تسجيل الدخول"""
        self.close()
        
    def get_user(self):
        """الحصول على المستخدم المسجل"""
        return getattr(self, 'user', None)


class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(f'النظام المحاسبي الموحد - مرحباً {self.user.account_name}')
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد النافذة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # تخطيط رئيسي
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # إنشاء التبويبات
        self.create_tabs()
        layout.addWidget(self.tabs)
        
        # إنشاء القوائم
        self.create_menus()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء شريط الحالة
        self.create_statusbar()
        
        # تطبيق الأنماط
        self.apply_styles()
        
    def create_tabs(self):
        """إنشاء التبويبات الرئيسية"""
        self.tabs = QTabWidget()
        
        # تبويب لوحة المعلومات
        dashboard_tab = QWidget()
        dashboard_layout = QVBoxLayout()
        dashboard_layout.addWidget(QLabel('لوحة المعلومات - قيد التطوير'))
        dashboard_tab.setLayout(dashboard_layout)
        self.tabs.addTab(dashboard_tab, 'لوحة المعلومات')
        
        # تبويب الموظفين
        employees_tab = QWidget()
        employees_layout = QVBoxLayout()
        employees_layout.addWidget(QLabel('إدارة الموظفين - قيد التطوير'))
        employees_tab.setLayout(employees_layout)
        self.tabs.addTab(employees_tab, 'الموظفين')
        
        # تبويب الرواتب
        payroll_tab = QWidget()
        payroll_layout = QVBoxLayout()
        payroll_layout.addWidget(QLabel('إدارة الرواتب - قيد التطوير'))
        payroll_tab.setLayout(payroll_layout)
        self.tabs.addTab(payroll_tab, 'الرواتب')
        
        # تبويب المحاسبة
        accounting_tab = QWidget()
        accounting_layout = QVBoxLayout()
        accounting_layout.addWidget(QLabel('المحاسبة - قيد التطوير'))
        accounting_tab.setLayout(accounting_layout)
        self.tabs.addTab(accounting_tab, 'المحاسبة')
        
        # تبويب التقارير
        reports_tab = QWidget()
        reports_layout = QVBoxLayout()
        reports_layout.addWidget(QLabel('التقارير - قيد التطوير'))
        reports_tab.setLayout(reports_layout)
        self.tabs.addTab(reports_tab, 'التقارير')
        
    def create_menus(self):
        """إنشاء القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        # قائمة الإعدادات
        settings_menu = menubar.addMenu('الإعدادات')
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        # إضافة إجراءات
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # إضافة أزرار سريعة
        dashboard_action = QAction('لوحة المعلومات', self)
        dashboard_action.triggered.connect(lambda: self.tabs.setCurrentIndex(0))
        toolbar.addAction(dashboard_action)
        
        employees_action = QAction('الموظفين', self)
        employees_action.triggered.connect(lambda: self.tabs.setCurrentIndex(1))
        toolbar.addAction(employees_action)
        
        payroll_action = QAction('الرواتب', self)
        payroll_action.triggered.connect(lambda: self.tabs.setCurrentIndex(2))
        toolbar.addAction(payroll_action)
        
    def create_statusbar(self):
        """إنشاء شريط الحالة"""
        statusbar = self.statusBar()
        statusbar.showMessage(f'مرحباً {self.user.account_name} - النظام المحاسبي الموحد')
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e9ecef;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007bff;
            }
            QMenuBar {
                background-color: #343a40;
                color: white;
                padding: 5px;
            }
            QMenuBar::item {
                padding: 8px 12px;
                border-radius: 3px;
            }
            QMenuBar::item:selected {
                background-color: #495057;
            }
            QToolBar {
                background-color: #6c757d;
                border: none;
                spacing: 3px;
            }
            QStatusBar {
                background-color: #e9ecef;
                border-top: 1px solid #dee2e6;
            }
        """)
        
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(self, 'حول البرنامج', 
                         'النظام المحاسبي الموحد - رواتب الموظفين\n'
                         'الإصدار 1.0\n'
                         'تطوير: فريق التطوير\n'
                         '© 2024 جميع الحقوق محفوظة')


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setApplicationName('النظام المحاسبي الموحد')
    app.setApplicationVersion('1.0')
    
    # تعيين الخط العربي
    font = QFont('Arial', 10)
    app.setFont(font)
    
    # عرض شاشة البداية
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    splash = QSplashScreen(splash_pix)
    splash.show()
    splash.showMessage('جاري تحميل النظام المحاسبي الموحد...', Qt.AlignCenter | Qt.AlignBottom)
    
    app.processEvents()
    
    # محاكاة تحميل
    import time
    time.sleep(2)
    
    splash.close()
    
    # عرض نافذة تسجيل الدخول
    login_dialog = LoginDialog()
    login_dialog.show()
    
    # انتظار تسجيل الدخول
    app.exec_()
    
    user = login_dialog.get_user()
    if user:
        # عرض النافذة الرئيسية
        main_window = MainWindow(user)
        main_window.show()
        
        sys.exit(app.exec_())


if __name__ == '__main__':
    main()
