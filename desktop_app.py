#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النظام المحاسبي الموحد - رواتب الموظفين
واجهة سطح المكتب الرئيسية
"""

import sys
import os
import django
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QPushButton, QMessageBox,
                             QListWidget, QListWidgetItem, QStackedWidget,
                             QFrame, QGridLayout, QSplashScreen, QLineEdit, QFormLayout)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QPixmap, QFont

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'payroll_system.settings')
django.setup()

from django.contrib.auth import authenticate
from users.models import CustomUser
from desktop_styles import (get_main_stylesheet, get_page_title_stylesheet,
                           get_page_description_stylesheet, get_sidebar_header_stylesheet,
                           get_user_info_stylesheet, get_development_notice_stylesheet,
                           get_content_frame_stylesheet, get_stat_value_stylesheet,
                           get_stat_title_stylesheet, get_stat_icon_stylesheet,
                           get_enhanced_button_stylesheet, get_modern_card_stylesheet,
                           get_floating_card_stylesheet, get_modern_activity_stylesheet,
                           get_welcome_banner_stylesheet)


class LoginDialog(QWidget):
    """نافذة تسجيل الدخول"""
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('تسجيل الدخول - النظام المحاسبي الموحد')
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # تخطيط رئيسي
        layout = QVBoxLayout()
        
        # شعار النظام
        logo_label = QLabel()
        logo_label.setText('النظام المحاسبي الموحد\nرواتب الموظفين')
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(logo_label)
        
        # حقول تسجيل الدخول
        form_layout = QFormLayout()
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText('اسم المستخدم')
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin: 5px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('كلمة المرور')
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.username_input.styleSheet())
        
        form_layout.addRow('اسم المستخدم:', self.username_input)
        form_layout.addRow('كلمة المرور:', self.password_input)
        
        layout.addLayout(form_layout)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton('تسجيل الدخول')
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.login_button.clicked.connect(self.login)
        
        self.cancel_button = QPushButton('إلغاء')
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.cancel_button.clicked.connect(self.close)
        
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # ربط Enter بتسجيل الدخول
        self.password_input.returnPressed.connect(self.login)
        
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, 'خطأ', 'يرجى إدخال اسم المستخدم وكلمة المرور')
            return
            
        try:
            user = authenticate(username=username, password=password)
            if user and user.is_active:
                self.user = user
                self.accept()
            else:
                QMessageBox.warning(self, 'خطأ', 'اسم المستخدم أو كلمة المرور غير صحيحة')
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تسجيل الدخول:\n{str(e)}')
    
    def accept(self):
        """قبول تسجيل الدخول"""
        self.close()
        
    def get_user(self):
        """الحصول على المستخدم المسجل"""
        return getattr(self, 'user', None)


class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.current_page = 'dashboard'
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle('نظام إدارة الرواتب والمحاسبة')
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)

        # إعداد النافذة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # تخطيط رئيسي أفقي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        central_widget.setLayout(main_layout)

        # إنشاء القائمة الجانبية
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)

        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        main_layout.addWidget(self.main_content)

        # إنشاء شريط الحالة
        self.create_statusbar()

        # تطبيق الأنماط
        self.apply_styles()

        # عرض الصفحة الرئيسية
        self.show_dashboard()
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(280)
        self.sidebar.setFrameStyle(QFrame.StyledPanel)

        sidebar_layout = QVBoxLayout()
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        self.sidebar.setLayout(sidebar_layout)

        # شعار النظام
        header = QWidget()
        header.setFixedHeight(100)
        header.setStyleSheet(get_sidebar_header_stylesheet())
        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(15, 20, 15, 20)

        # أيقونة النظام
        icon_label = QLabel('💼')
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                background: transparent;
                margin-bottom: 5px;
            }
        """)
        header_layout.addWidget(icon_label)

        title_label = QLabel('نظام إدارة الرواتب\nوالمحاسبة')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffd700;
                font-size: 14px;
                font-weight: 700;
                background: transparent;
                line-height: 1.2;
            }
        """)
        header_layout.addWidget(title_label)
        header.setLayout(header_layout)
        sidebar_layout.addWidget(header)

        # قائمة التنقل
        self.nav_list = QListWidget()
        self.nav_list.setFrameStyle(QFrame.NoFrame)

        # إضافة عناصر القائمة
        nav_items = [
            ('الصفحة الرئيسية', 'dashboard', '🏠'),
            ('إدارة المستخدمين', 'users', '👥'),
            ('البيانات والتوثيق', 'data_docs', '📋'),
            ('إدارة الموظفين', 'employees', '👤'),
            ('هياكل المرتبات', 'salaries', '💰'),
            ('معالجة الرواتب', 'payroll', '💵'),
            ('التقارير والإحصائيات', 'reports', '📊'),
            ('المساعدة والدعم', 'programs', '📖')
        ]

        for text, key, icon in nav_items:
            item = QListWidgetItem(f'{icon}  {text}')
            item.setData(Qt.UserRole, key)
            item.setSizeHint(QSize(260, 50))
            self.nav_list.addItem(item)

        self.nav_list.itemClicked.connect(self.on_nav_item_clicked)
        self.nav_list.setCurrentRow(0)  # تحديد العنصر الأول

        sidebar_layout.addWidget(self.nav_list)

        # مساحة فارغة في الأسفل
        sidebar_layout.addStretch()

        # معلومات المستخدم
        user_info = QWidget()
        user_info.setFixedHeight(80)
        user_info.setStyleSheet(get_user_info_stylesheet())
        user_info_layout = QVBoxLayout()
        user_info_layout.setContentsMargins(20, 15, 20, 15)

        # أيقونة المستخدم
        user_icon = QLabel('👤')
        user_icon.setAlignment(Qt.AlignCenter)
        user_icon.setStyleSheet("""
            QLabel {
                font-size: 20px;
                background: transparent;
                margin-bottom: 5px;
            }
        """)
        user_info_layout.addWidget(user_icon)

        user_label = QLabel(f'مرحباً، {self.user.account_name}')
        user_label.setAlignment(Qt.AlignCenter)
        user_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 13px;
                font-weight: 500;
                background: transparent;
            }
        """)
        user_info_layout.addWidget(user_label)
        user_info.setLayout(user_info_layout)
        sidebar_layout.addWidget(user_info)

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        self.main_content = QStackedWidget()

        # إنشاء الصفحات المختلفة
        self.pages = {}

        # الصفحة الرئيسية
        self.pages['dashboard'] = self.create_dashboard_page()
        self.main_content.addWidget(self.pages['dashboard'])

        # صفحة المستخدمين
        self.pages['users'] = self.create_users_page()
        self.main_content.addWidget(self.pages['users'])

        # صفحة البيانات والتوثيق
        self.pages['data_docs'] = self.create_data_docs_page()
        self.main_content.addWidget(self.pages['data_docs'])

        # صفحة الموظفين
        self.pages['employees'] = self.create_employees_page()
        self.main_content.addWidget(self.pages['employees'])

        # صفحة المرتبات
        self.pages['salaries'] = self.create_salaries_page()
        self.main_content.addWidget(self.pages['salaries'])

        # صفحة الرواتب
        self.pages['payroll'] = self.create_payroll_page()
        self.main_content.addWidget(self.pages['payroll'])

        # صفحة التقارير
        self.pages['reports'] = self.create_reports_page()
        self.main_content.addWidget(self.pages['reports'])

        # صفحة دليل البرامج
        self.pages['programs'] = self.create_programs_page()
        self.main_content.addWidget(self.pages['programs'])

    def on_nav_item_clicked(self, item):
        """معالج النقر على عنصر في القائمة الجانبية"""
        page_key = item.data(Qt.UserRole)
        self.current_page = page_key

        # تغيير الصفحة المعروضة
        if page_key in self.pages:
            page_index = list(self.pages.keys()).index(page_key)
            self.main_content.setCurrentIndex(page_index)

            # تحديث شريط الحالة
            page_names = {
                'dashboard': 'الصفحة الرئيسية',
                'users': 'إدارة المستخدمين',
                'data_docs': 'البيانات والتوثيق',
                'employees': 'إدارة الموظفين',
                'salaries': 'إدارة المرتبات',
                'payroll': 'إدارة الرواتب',
                'reports': 'التقارير والإحصائيات',
                'programs': 'دليل البرامج'
            }

            status_text = f'مرحباً {self.user.account_name} - {page_names.get(page_key, "صفحة غير معروفة")}'
            self.statusBar().showMessage(status_text)

    def create_dashboard_page(self):
        """إنشاء الصفحة الرئيسية"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # شعار الترحيب
        welcome_banner = QLabel(f'🎉 مرحباً بك، {self.user.account_name}!\n💼 لوحة المعلومات الرئيسية')
        welcome_banner.setAlignment(Qt.AlignCenter)
        welcome_banner.setStyleSheet(get_welcome_banner_stylesheet())
        layout.addWidget(welcome_banner)

        # حاوية البطاقات الإحصائية
        stats_container = QWidget()
        stats_container.setStyleSheet(get_floating_card_stylesheet())
        stats_container_layout = QVBoxLayout()
        stats_container_layout.setContentsMargins(0, 0, 0, 0)

        # عنوان القسم
        stats_title = QLabel('📊 الإحصائيات السريعة')
        stats_title.setAlignment(Qt.AlignCenter)
        stats_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: 700;
                color: #1e3c72;
                margin: 15px 0;
                background: transparent;
            }
        """)
        stats_container_layout.addWidget(stats_title)

        # بطاقات الإحصائيات
        stats_layout = QGridLayout()
        stats_layout.setSpacing(25)

        # بطاقة إجمالي الموظفين
        employees_card = self.create_premium_stat_card('إجمالي الموظفين', '150', '#1e3c72', '👥')
        stats_layout.addWidget(employees_card, 0, 0)

        # بطاقة الرواتب الشهرية
        salary_card = self.create_premium_stat_card('الرواتب الشهرية', '2,500,000 ريال', '#27ae60', '💰')
        stats_layout.addWidget(salary_card, 0, 1)

        # بطاقة الإجازات المعلقة
        leave_card = self.create_premium_stat_card('الإجازات المعلقة', '25', '#e74c3c', '📅')
        stats_layout.addWidget(leave_card, 0, 2)

        # بطاقة التقارير الجديدة
        reports_card = self.create_premium_stat_card('التقارير الجديدة', '8', '#f39c12', '📊')
        stats_layout.addWidget(reports_card, 0, 3)

        stats_container_layout.addLayout(stats_layout)
        stats_container.setLayout(stats_container_layout)
        layout.addWidget(stats_container)

        # الأنشطة الأخيرة
        activities_frame = QFrame()
        activities_frame.setFrameStyle(QFrame.StyledPanel)
        activities_frame.setStyleSheet(get_floating_card_stylesheet())
        activities_layout = QVBoxLayout()
        activities_layout.setContentsMargins(30, 25, 30, 25)
        activities_layout.setSpacing(15)

        activities_title = QLabel('📋 الأنشطة الأخيرة')
        activities_title.setAlignment(Qt.AlignCenter)
        activities_title.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: 700;
                color: #1e3c72;
                margin-bottom: 20px;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 215, 0, 0.1), stop:0.5 rgba(255, 215, 0, 0.05), stop:1 rgba(255, 215, 0, 0.1));
                border-radius: 10px;
                border: 2px solid rgba(30, 60, 114, 0.1);
            }
        """)
        activities_layout.addWidget(activities_title)

        # قائمة الأنشطة
        activities = [
            '✅ تم إضافة موظف جديد: أحمد محمد علي - قسم المحاسبة',
            '💰 تم معالجة رواتب شهر ديسمبر 2024 بنجاح',
            '📊 تم إنشاء تقرير الحضور والانصراف الشهري',
            '🔄 تم تحديث بيانات الموظف: فاطمة علي أحمد',
            '📋 تم إنشاء طلب إجازة جديد - موظف: سارة محمد',
            '🎯 تم اعتماد مسير رواتب شهر نوفمبر',
            '📈 تم إنشاء تقرير الأداء المالي الربعي'
        ]

        for activity in activities:
            activity_label = QLabel(activity)
            activity_label.setStyleSheet(get_modern_activity_stylesheet())
            activities_layout.addWidget(activity_label)

        activities_frame.setLayout(activities_layout)
        layout.addWidget(activities_frame)

        layout.addStretch()
        page.setLayout(layout)
        return page

    def create_stat_card(self, title, value, color, icon):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setFixedHeight(120)

        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(20, 15, 20, 15)

        # الأيقونة والقيمة
        top_layout = QHBoxLayout()

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(get_stat_icon_stylesheet(color))

        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignRight)
        value_label.setStyleSheet(get_stat_value_stylesheet(color))

        top_layout.addWidget(icon_label)
        top_layout.addStretch()
        top_layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(get_stat_title_stylesheet())

        card_layout.addLayout(top_layout)
        card_layout.addWidget(title_label)
        card_layout.addStretch()

        card.setLayout(card_layout)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 12px;
                border-left: 4px solid {color};
                padding: 0px;
            }}
            QFrame:hover {{
                border-color: {color};
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }}
        """)

        return card

    def create_enhanced_stat_card(self, title, value, color, icon):
        """إنشاء بطاقة إحصائية محسنة"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setFixedHeight(140)
        card.setStyleSheet(get_modern_card_stylesheet(color))

        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(25, 20, 25, 20)
        card_layout.setSpacing(15)

        # الصف العلوي - الأيقونة والقيمة
        top_layout = QHBoxLayout()

        # الأيقونة مع خلفية دائرية
        icon_container = QFrame()
        icon_container.setFixedSize(70, 70)
        icon_container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 rgba(255,255,255,0.3));
                border-radius: 35px;
                border: 3px solid rgba(255,255,255,0.8);
            }}
        """)

        icon_layout = QVBoxLayout()
        icon_layout.setContentsMargins(0, 0, 0, 0)
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                background: transparent;
                color: white;
            }
        """)
        icon_layout.addWidget(icon_label)
        icon_container.setLayout(icon_layout)

        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: 800;
                color: {color};
                background: transparent;
            }}
        """)

        top_layout.addWidget(icon_container)
        top_layout.addStretch()
        top_layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: 600;
                color: #495057;
                background: transparent;
                margin-top: 10px;
            }
        """)

        card_layout.addLayout(top_layout)
        card_layout.addWidget(title_label)

        card.setLayout(card_layout)
        return card

    def create_users_page(self):
        """إنشاء صفحة المستخدمين"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # عنوان الصفحة
        title = QLabel('👥 إدارة المستخدمين')
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(get_page_title_stylesheet())
        layout.addWidget(title)

        # وصف الصفحة
        desc = QLabel('إدارة حسابات المستخدمين وصلاحياتهم في النظام')
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet(get_page_description_stylesheet())
        layout.addWidget(desc)

        # بطاقات المستخدمين
        users_container = QFrame()
        users_container.setStyleSheet(get_floating_card_stylesheet())
        users_layout = QVBoxLayout()
        users_layout.setContentsMargins(25, 25, 25, 25)

        # إحصائيات المستخدمين
        stats_layout = QHBoxLayout()

        # إجمالي المستخدمين
        total_users = self.create_info_card('إجمالي المستخدمين', '15', '#3498db', '👤')
        stats_layout.addWidget(total_users)

        # المستخدمين النشطين
        active_users = self.create_info_card('المستخدمين النشطين', '12', '#27ae60', '✅')
        stats_layout.addWidget(active_users)

        # المستخدمين المعطلين
        inactive_users = self.create_info_card('المستخدمين المعطلين', '3', '#e74c3c', '❌')
        stats_layout.addWidget(inactive_users)

        users_layout.addLayout(stats_layout)

        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        add_user_btn = QPushButton('➕ إضافة مستخدم جديد')
        add_user_btn.setStyleSheet(get_enhanced_button_stylesheet('#28a745', '#34ce57'))

        manage_roles_btn = QPushButton('🔐 إدارة الأدوار')
        manage_roles_btn.setStyleSheet(get_enhanced_button_stylesheet('#6f42c1', '#5a32a3'))

        user_permissions_btn = QPushButton('🛡️ الصلاحيات')
        user_permissions_btn.setStyleSheet(get_enhanced_button_stylesheet('#17a2b8', '#138496'))

        user_reports_btn = QPushButton('📊 تقارير المستخدمين')
        user_reports_btn.setStyleSheet(get_enhanced_button_stylesheet('#fd7e14', '#e55100'))

        buttons_layout.addWidget(add_user_btn)
        buttons_layout.addWidget(manage_roles_btn)
        buttons_layout.addWidget(user_permissions_btn)
        buttons_layout.addWidget(user_reports_btn)

        users_layout.addLayout(buttons_layout)

        # قائمة المستخدمين الأخيرين
        recent_title = QLabel('👥 المستخدمين المضافين مؤخراً')
        recent_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: #1e3c72;
                margin: 20px 0 10px 0;
                background: transparent;
            }
        """)
        users_layout.addWidget(recent_title)

        recent_users = [
            '👤 أحمد محمد - مدير المحاسبة',
            '👤 فاطمة علي - محاسب أول',
            '👤 محمد أحمد - موظف رواتب',
            '👤 سارة محمود - مدخل بيانات'
        ]

        for user in recent_users:
            user_label = QLabel(user)
            user_label.setStyleSheet(get_modern_activity_stylesheet())
            users_layout.addWidget(user_label)

        users_container.setLayout(users_layout)
        layout.addWidget(users_container)

        layout.addStretch()
        page.setLayout(layout)
        return page

    def create_info_card(self, title, value, color, icon):
        """إنشاء بطاقة معلوماتية صغيرة"""
        card = QFrame()
        card.setFixedHeight(100)
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid {color};
                border-radius: 15px;
                padding: 0px;
            }}
            QFrame:hover {{
                box-shadow: 0 8px 20px rgba(0,0,0,0.15);
                transform: translateY(-3px);
            }}
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)

        # الأيقونة والقيمة
        top_layout = QHBoxLayout()

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                color: {color};
                background: transparent;
            }}
        """)

        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignRight)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {color};
                background: transparent;
            }}
        """)

        top_layout.addWidget(icon_label)
        top_layout.addStretch()
        top_layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                background: transparent;
                font-weight: 500;
            }
        """)

        layout.addLayout(top_layout)
        layout.addWidget(title_label)

        card.setLayout(layout)
        return card

    def create_data_docs_page(self):
        """إنشاء صفحة البيانات والتوثيق"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # عنوان الصفحة
        title = QLabel('📋 البيانات والتوثيق')
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(get_page_title_stylesheet())
        layout.addWidget(title)

        # وصف الصفحة
        desc = QLabel('إدارة البيانات الأساسية ووثائق النظام والمرجعيات')
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet(get_page_description_stylesheet())
        layout.addWidget(desc)

        # قسم البيانات الأساسية
        basic_data_frame = QFrame()
        basic_data_frame.setStyleSheet(get_floating_card_stylesheet())
        basic_layout = QVBoxLayout()
        basic_layout.setContentsMargins(25, 25, 25, 25)

        basic_title = QLabel('🏢 البيانات الأساسية')
        basic_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: 700;
                color: #1e3c72;
                margin-bottom: 15px;
                background: transparent;
            }
        """)
        basic_layout.addWidget(basic_title)

        # أزرار البيانات الأساسية
        basic_buttons_layout = QGridLayout()
        basic_buttons_layout.setSpacing(15)

        company_btn = QPushButton('🏢 بيانات الشركة')
        company_btn.setStyleSheet(get_enhanced_button_stylesheet('#007bff', '#0056b3'))
        basic_buttons_layout.addWidget(company_btn, 0, 0)

        departments_btn = QPushButton('🏛️ الأقسام والإدارات')
        departments_btn.setStyleSheet(get_enhanced_button_stylesheet('#28a745', '#1e7e34'))
        basic_buttons_layout.addWidget(departments_btn, 0, 1)

        positions_btn = QPushButton('💼 المناصب الوظيفية')
        positions_btn.setStyleSheet(get_enhanced_button_stylesheet('#17a2b8', '#117a8b'))
        basic_buttons_layout.addWidget(positions_btn, 1, 0)

        currencies_btn = QPushButton('💱 العملات')
        currencies_btn.setStyleSheet(get_enhanced_button_stylesheet('#ffc107', '#e0a800'))
        basic_buttons_layout.addWidget(currencies_btn, 1, 1)

        basic_layout.addLayout(basic_buttons_layout)
        basic_data_frame.setLayout(basic_layout)
        layout.addWidget(basic_data_frame)

        # قسم التوثيق
        docs_frame = QFrame()
        docs_frame.setStyleSheet(get_floating_card_stylesheet())
        docs_layout = QVBoxLayout()
        docs_layout.setContentsMargins(25, 25, 25, 25)

        docs_title = QLabel('📚 التوثيق والمساعدة')
        docs_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: 700;
                color: #1e3c72;
                margin-bottom: 15px;
                background: transparent;
            }
        """)
        docs_layout.addWidget(docs_title)

        # أزرار التوثيق
        docs_buttons_layout = QGridLayout()
        docs_buttons_layout.setSpacing(15)

        user_manual_btn = QPushButton('📖 دليل المستخدم')
        user_manual_btn.setStyleSheet(get_enhanced_button_stylesheet('#6f42c1', '#5a32a3'))
        docs_buttons_layout.addWidget(user_manual_btn, 0, 0)

        system_docs_btn = QPushButton('⚙️ وثائق النظام')
        system_docs_btn.setStyleSheet(get_enhanced_button_stylesheet('#fd7e14', '#e55100'))
        docs_buttons_layout.addWidget(system_docs_btn, 0, 1)

        backup_btn = QPushButton('💾 النسخ الاحتياطي')
        backup_btn.setStyleSheet(get_enhanced_button_stylesheet('#20c997', '#1aa179'))
        docs_buttons_layout.addWidget(backup_btn, 1, 0)

        settings_btn = QPushButton('🔧 إعدادات النظام')
        settings_btn.setStyleSheet(get_enhanced_button_stylesheet('#dc3545', '#c82333'))
        docs_buttons_layout.addWidget(settings_btn, 1, 1)

        docs_layout.addLayout(docs_buttons_layout)
        docs_frame.setLayout(docs_layout)
        layout.addWidget(docs_frame)

        layout.addStretch()
        page.setLayout(layout)
        return page

    def create_employees_page(self):
        """إنشاء صفحة الموظفين"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # عنوان الصفحة
        title = QLabel('👤 إدارة الموظفين')
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(get_page_title_stylesheet())
        layout.addWidget(title)

        # وصف الصفحة
        desc = QLabel('إضافة وتعديل وإدارة بيانات الموظفين والهيكل التنظيمي')
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet(get_page_description_stylesheet())
        layout.addWidget(desc)

        # إحصائيات الموظفين
        stats_container = QFrame()
        stats_container.setStyleSheet(get_floating_card_stylesheet())
        stats_layout = QVBoxLayout()
        stats_layout.setContentsMargins(25, 25, 25, 25)

        stats_title = QLabel('📊 إحصائيات الموظفين')
        stats_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: 700;
                color: #1e3c72;
                margin-bottom: 15px;
                background: transparent;
            }
        """)
        stats_layout.addWidget(stats_title)

        # بطاقات الإحصائيات
        emp_stats_layout = QHBoxLayout()

        total_emp = self.create_info_card('إجمالي الموظفين', '150', '#3498db', '👥')
        emp_stats_layout.addWidget(total_emp)

        active_emp = self.create_info_card('الموظفين النشطين', '142', '#27ae60', '✅')
        emp_stats_layout.addWidget(active_emp)

        new_emp = self.create_info_card('موظفين جدد هذا الشهر', '8', '#f39c12', '🆕')
        emp_stats_layout.addWidget(new_emp)

        on_leave = self.create_info_card('في إجازة', '12', '#e74c3c', '🏖️')
        emp_stats_layout.addWidget(on_leave)

        stats_layout.addLayout(emp_stats_layout)
        stats_container.setLayout(stats_layout)
        layout.addWidget(stats_container)

        # عمليات الموظفين
        operations_frame = QFrame()
        operations_frame.setStyleSheet(get_floating_card_stylesheet())
        operations_layout = QVBoxLayout()
        operations_layout.setContentsMargins(25, 25, 25, 25)

        operations_title = QLabel('⚙️ عمليات الموظفين')
        operations_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: 700;
                color: #1e3c72;
                margin-bottom: 15px;
                background: transparent;
            }
        """)
        operations_layout.addWidget(operations_title)

        # أزرار العمليات
        operations_buttons_layout = QGridLayout()
        operations_buttons_layout.setSpacing(15)

        add_emp_btn = QPushButton('➕ إضافة موظف جديد')
        add_emp_btn.setStyleSheet(get_enhanced_button_stylesheet('#28a745', '#1e7e34'))
        operations_buttons_layout.addWidget(add_emp_btn, 0, 0)

        search_emp_btn = QPushButton('🔍 البحث عن موظف')
        search_emp_btn.setStyleSheet(get_enhanced_button_stylesheet('#007bff', '#0056b3'))
        operations_buttons_layout.addWidget(search_emp_btn, 0, 1)

        import_excel_btn = QPushButton('📊 استيراد من Excel')
        import_excel_btn.setStyleSheet(get_enhanced_button_stylesheet('#17a2b8', '#117a8b'))
        operations_buttons_layout.addWidget(import_excel_btn, 1, 0)

        export_excel_btn = QPushButton('📤 تصدير إلى Excel')
        export_excel_btn.setStyleSheet(get_enhanced_button_stylesheet('#6f42c1', '#5a32a3'))
        operations_buttons_layout.addWidget(export_excel_btn, 1, 1)

        emp_reports_btn = QPushButton('📋 تقارير الموظفين')
        emp_reports_btn.setStyleSheet(get_enhanced_button_stylesheet('#fd7e14', '#e55100'))
        operations_buttons_layout.addWidget(emp_reports_btn, 2, 0)

        emp_cards_btn = QPushButton('🆔 بطاقات الموظفين')
        emp_cards_btn.setStyleSheet(get_enhanced_button_stylesheet('#20c997', '#1aa179'))
        operations_buttons_layout.addWidget(emp_cards_btn, 2, 1)

        operations_layout.addLayout(operations_buttons_layout)
        operations_frame.setLayout(operations_layout)
        layout.addWidget(operations_frame)

        layout.addStretch()
        page.setLayout(layout)
        return page

    def create_salaries_page(self):
        """إنشاء صفحة المرتبات"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # عنوان الصفحة
        title = QLabel('💰 إدارة المرتبات')
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(get_page_title_stylesheet())
        layout.addWidget(title)

        # وصف الصفحة
        desc = QLabel('تحديد وإدارة هياكل المرتبات والعلاوات والدرجات الوظيفية')
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet(get_page_description_stylesheet())
        layout.addWidget(desc)

        # هياكل المرتبات
        salary_structure_frame = QFrame()
        salary_structure_frame.setStyleSheet(get_floating_card_stylesheet())
        structure_layout = QVBoxLayout()
        structure_layout.setContentsMargins(25, 25, 25, 25)

        structure_title = QLabel('🏗️ هياكل المرتبات')
        structure_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: 700;
                color: #1e3c72;
                margin-bottom: 15px;
                background: transparent;
            }
        """)
        structure_layout.addWidget(structure_title)

        # إحصائيات المرتبات
        salary_stats_layout = QHBoxLayout()

        total_grades = self.create_info_card('إجمالي الدرجات', '12', '#3498db', '📊')
        salary_stats_layout.addWidget(total_grades)

        avg_salary = self.create_info_card('متوسط الراتب', '15,000', '#27ae60', '💵')
        salary_stats_layout.addWidget(avg_salary)

        max_salary = self.create_info_card('أعلى راتب', '35,000', '#e74c3c', '⬆️')
        salary_stats_layout.addWidget(max_salary)

        min_salary = self.create_info_card('أقل راتب', '8,000', '#f39c12', '⬇️')
        salary_stats_layout.addWidget(min_salary)

        structure_layout.addLayout(salary_stats_layout)

        # أزرار إدارة المرتبات
        salary_buttons_layout = QGridLayout()
        salary_buttons_layout.setSpacing(15)

        grades_btn = QPushButton('📊 الدرجات الوظيفية')
        grades_btn.setStyleSheet(get_enhanced_button_stylesheet('#007bff', '#0056b3'))
        salary_buttons_layout.addWidget(grades_btn, 0, 0)

        allowances_btn = QPushButton('💰 البدلات والعلاوات')
        allowances_btn.setStyleSheet(get_enhanced_button_stylesheet('#28a745', '#1e7e34'))
        salary_buttons_layout.addWidget(allowances_btn, 0, 1)

        deductions_btn = QPushButton('➖ الاستقطاعات')
        deductions_btn.setStyleSheet(get_enhanced_button_stylesheet('#dc3545', '#c82333'))
        salary_buttons_layout.addWidget(deductions_btn, 1, 0)

        tax_brackets_btn = QPushButton('🏛️ الشرائح الضريبية')
        tax_brackets_btn.setStyleSheet(get_enhanced_button_stylesheet('#6f42c1', '#5a32a3'))
        salary_buttons_layout.addWidget(tax_brackets_btn, 1, 1)

        structure_layout.addLayout(salary_buttons_layout)
        salary_structure_frame.setLayout(structure_layout)
        layout.addWidget(salary_structure_frame)

        # تقارير المرتبات
        reports_frame = QFrame()
        reports_frame.setStyleSheet(get_floating_card_stylesheet())
        reports_layout = QVBoxLayout()
        reports_layout.setContentsMargins(25, 25, 25, 25)

        reports_title = QLabel('📈 تقارير المرتبات')
        reports_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: 700;
                color: #1e3c72;
                margin-bottom: 15px;
                background: transparent;
            }
        """)
        reports_layout.addWidget(reports_title)

        # أزرار التقارير
        reports_buttons_layout = QHBoxLayout()
        reports_buttons_layout.setSpacing(15)

        salary_analysis_btn = QPushButton('📊 تحليل المرتبات')
        salary_analysis_btn.setStyleSheet(get_enhanced_button_stylesheet('#17a2b8', '#117a8b'))
        reports_buttons_layout.addWidget(salary_analysis_btn)

        comparison_btn = QPushButton('⚖️ مقارنة المرتبات')
        comparison_btn.setStyleSheet(get_enhanced_button_stylesheet('#fd7e14', '#e55100'))
        reports_buttons_layout.addWidget(comparison_btn)

        budget_btn = QPushButton('💼 ميزانية المرتبات')
        budget_btn.setStyleSheet(get_enhanced_button_stylesheet('#20c997', '#1aa179'))
        reports_buttons_layout.addWidget(budget_btn)

        reports_layout.addLayout(reports_buttons_layout)
        reports_frame.setLayout(reports_layout)
        layout.addWidget(reports_frame)

        layout.addStretch()
        page.setLayout(layout)
        return page

    def create_payroll_page(self):
        """إنشاء صفحة الرواتب"""
        return self.create_simple_page('إدارة الرواتب', 'معالجة وحساب رواتب الموظفين الشهرية')

    def create_reports_page(self):
        """إنشاء صفحة التقارير"""
        return self.create_simple_page('التقارير والإحصائيات', 'إنشاء وعرض التقارير المالية والإدارية')

    def create_programs_page(self):
        """إنشاء صفحة دليل البرامج"""
        return self.create_simple_page('دليل البرامج', 'دليل استخدام النظام والمساعدة')

    def create_simple_page(self, title, description):
        """إنشاء صفحة بسيطة"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # عنوان الصفحة
        title_label = QLabel(title)
        title_label.setStyleSheet(get_page_title_stylesheet())
        layout.addWidget(title_label)

        # وصف الصفحة
        desc_label = QLabel(description)
        desc_label.setStyleSheet(get_page_description_stylesheet())
        layout.addWidget(desc_label)

        # محتوى الصفحة
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.StyledPanel)
        content_frame.setStyleSheet(get_content_frame_stylesheet())

        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)

        # رسالة قيد التطوير
        dev_label = QLabel('🚧 هذه الصفحة قيد التطوير')
        dev_label.setAlignment(Qt.AlignCenter)
        dev_label.setStyleSheet(get_development_notice_stylesheet())
        content_layout.addWidget(dev_label)

        # أزرار العمليات الأساسية
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        add_btn = QPushButton('➕ إضافة جديد')
        add_btn.setStyleSheet(get_enhanced_button_stylesheet('#28a745', '#34ce57'))

        edit_btn = QPushButton('✏️ تعديل')
        edit_btn.setStyleSheet(get_enhanced_button_stylesheet('#007bff', '#0056b3'))

        delete_btn = QPushButton('🗑️ حذف')
        delete_btn.setStyleSheet(get_enhanced_button_stylesheet('#dc3545', '#c82333'))

        report_btn = QPushButton('📊 تقرير')
        report_btn.setStyleSheet(get_enhanced_button_stylesheet('#6f42c1', '#5a32a3'))

        buttons_layout.addWidget(add_btn)
        buttons_layout.addWidget(edit_btn)
        buttons_layout.addWidget(delete_btn)
        buttons_layout.addWidget(report_btn)
        buttons_layout.addStretch()

        content_layout.addLayout(buttons_layout)
        content_frame.setLayout(content_layout)
        layout.addWidget(content_frame)

        layout.addStretch()
        page.setLayout(layout)
        return page

    def get_button_style(self, color):
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                margin: 5px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
            QPushButton:pressed {{
                background-color: {color};
                opacity: 0.6;
            }}
        """

    def show_dashboard(self):
        """عرض الصفحة الرئيسية"""
        self.main_content.setCurrentWidget(self.pages['dashboard'])
        self.nav_list.setCurrentRow(0)


        
    def create_statusbar(self):
        """إنشاء شريط الحالة"""
        statusbar = self.statusBar()
        statusbar.showMessage(f'مرحباً {self.user.account_name} - النظام المحاسبي الموحد')
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(get_main_stylesheet())
        
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(self, 'حول البرنامج', 
                         'النظام المحاسبي الموحد - رواتب الموظفين\n'
                         'الإصدار 1.0\n'
                         'تطوير: فريق التطوير\n'
                         '© 2024 جميع الحقوق محفوظة')


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setApplicationName('النظام المحاسبي الموحد')
    app.setApplicationVersion('1.0')
    
    # تعيين الخط العربي
    font = QFont('Arial', 10)
    app.setFont(font)
    
    # عرض شاشة البداية
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    splash = QSplashScreen(splash_pix)
    splash.show()
    splash.showMessage('جاري تحميل النظام المحاسبي الموحد...', Qt.AlignCenter | Qt.AlignBottom)
    
    app.processEvents()
    
    # محاكاة تحميل
    import time
    time.sleep(2)
    
    splash.close()
    
    # عرض نافذة تسجيل الدخول
    login_dialog = LoginDialog()
    login_dialog.show()
    
    # انتظار تسجيل الدخول
    app.exec_()
    
    user = login_dialog.get_user()
    if user:
        # عرض النافذة الرئيسية
        main_window = MainWindow(user)
        main_window.show()
        
        sys.exit(app.exec_())


if __name__ == '__main__':
    main()
