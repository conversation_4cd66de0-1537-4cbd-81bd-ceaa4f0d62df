import datetime
from django.db import transaction
from core.models import Organization
from employees.models import Employee
from core.financial_models import FinancialTransaction

def generate_employee_number():
    """توليد رقم تسلسلي للموظف الجديد"""
    current_year = datetime.datetime.now().year % 100  # Last two digits of year
    prefix = f"E{current_year}"
    
    with transaction.atomic():
        # Get last employee number for current year
        last_employee = Employee.objects.filter(
            employee_number__startswith=prefix
        ).order_by('-employee_number').first()
        
        if last_employee:
            try:
                last_number = int(last_employee.employee_number[3:])
                new_number = last_number + 1
            except ValueError:
                new_number = 1
        else:
            new_number = 1
        
        return f"{prefix}{str(new_number).zfill(4)}"

def generate_transaction_number(transaction_type):
    """توليد رقم تسلسلي للمعاملة المالية"""
    current_year = datetime.datetime.now().year % 100
    current_month = datetime.datetime.now().month
    
    # Transaction type prefix
    prefix_map = {
        'payment': 'P',
        'receipt': 'R',
        'journal': 'J'
    }
    prefix = prefix_map.get(transaction_type, 'X')
    
    with transaction.atomic():
        # Get last transaction number for current month
        last_transaction = FinancialTransaction.objects.filter(
            transaction_number__startswith=f"{prefix}{current_year}{str(current_month).zfill(2)}"
        ).order_by('-transaction_number').first()
        
        if last_transaction:
            try:
                last_number = int(last_transaction.transaction_number[-4:])
                new_number = last_number + 1
            except ValueError:
                new_number = 1
        else:
            new_number = 1
        
        return f"{prefix}{current_year}{str(current_month).zfill(2)}{str(new_number).zfill(4)}"

def generate_account_number(account_type):
    """توليد رقم حساب جديد"""
    # Account type prefixes
    prefix_map = {
        'asset': '1',
        'liability': '2',
        'equity': '3',
        'revenue': '4',
        'expense': '5',
        'bank': '6',
        'cash': '7',
    }
    
    prefix = prefix_map.get(account_type, '9')
    
    with transaction.atomic():
        # Get last account number with this prefix
        last_account = AccountGuide.objects.filter(
            account_number__startswith=prefix
        ).order_by('-account_number').first()
        
        if last_account:
            try:
                last_number = int(last_account.account_number)
                new_number = last_number + 1
            except ValueError:
                new_number = int(f"{prefix}0001")
        else:
            new_number = int(f"{prefix}0001")
        
        return str(new_number)

def generate_salary_sheet_number(month, year):
    """توليد رقم كشف الراتب"""
    year_suffix = str(year)[-2:]  # Last two digits of year
    prefix = f"S{year_suffix}{str(month).zfill(2)}"
    
    with transaction.atomic():
        # Get last salary sheet number for this month/year
        last_sheet = SalarySheet.objects.filter(
            sheet_number__startswith=prefix
        ).order_by('-sheet_number').first()
        
        if last_sheet:
            try:
                last_number = int(last_sheet.sheet_number[-4:])
                new_number = last_number + 1
            except ValueError:
                new_number = 1
        else:
            new_number = 1
        
        return f"{prefix}{str(new_number).zfill(4)}"

def get_next_number(sequence_name):
    """الحصول على الرقم التسلسلي التالي لأي تسلسل"""
    org = Organization.objects.first()
    if not org:
        return "1"
    
    current_sequences = getattr(org, 'sequences', {})
    next_number = current_sequences.get(sequence_name, 0) + 1
    
    # تحديث التسلسل
    current_sequences[sequence_name] = next_number
    org.sequences = current_sequences
    org.save()
    
    return str(next_number)