from django.conf import settings
from .models import Organization, Activity
from .activity_models import SystemLog

def organization_info(request):
    """
    إضافة معلومات المؤسسة والإحصائيات الأساسية إلى سياق القوالب
    """
    context = {}
    
    # معلومات المؤسسة
    try:
        organization = Organization.objects.first()
        context['organization'] = organization
    except Organization.DoesNotExist:
        context['organization'] = None
    
    # إذا كان المستخدم مسجل الدخول
    if request.user.is_authenticated:
        # آخر النشاطات
        context['recent_activities'] = Activity.objects.select_related(
            'user'
        ).order_by('-created_at')[:5]
        
        # آخر الأخطاء في النظام (للمشرفين فقط)
        if request.user.is_superuser:
            context['recent_system_logs'] = SystemLog.objects.filter(
                level__in=['error', 'critical']
            ).order_by('-created_at')[:5]
        
        # معلومات المستخدم
        context['user_activities'] = Activity.objects.filter(
            user=request.user
        ).order_by('-created_at')[:5]
    
    # إعدادات عامة
    context['maintenance_mode'] = getattr(settings, 'MAINTENANCE_MODE', False)
    context['debug_mode'] = settings.DEBUG
    
    # معلومات النظام
    import psutil
    import os
    
    # استخدام المعالج والذاكرة
    context['cpu_usage'] = psutil.cpu_percent()
    context['memory_usage'] = psutil.virtual_memory().percent
    
    # حجم قاعدة البيانات وملفات الوسائط
    try:
        db_size = 0
        media_size = 0
        backup_size = 0
        
        # حجم الوسائط
        for dirpath, dirnames, filenames in os.walk(settings.MEDIA_ROOT):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                media_size += os.path.getsize(fp)
        
        # حجم النسخ الاحتياطية
        backup_dir = settings.BACKUP_DIR
        if os.path.exists(backup_dir):
            for dirpath, dirnames, filenames in os.walk(backup_dir):
                for f in filenames:
                    fp = os.path.join(dirpath, f)
                    backup_size += os.path.getsize(fp)
        
        context['media_size'] = media_size / (1024 * 1024)  # تحويل إلى ميجابايت
        context['backup_size'] = backup_size / (1024 * 1024)
        
    except Exception:
        # تجاهل أي أخطاء في حساب الأحجام
        pass
    
    return context