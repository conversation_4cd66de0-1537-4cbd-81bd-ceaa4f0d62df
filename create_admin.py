#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'payroll_system.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

# حذف المستخدم الإداري إذا كان موجوداً
if User.objects.filter(username='admin').exists():
    User.objects.filter(username='admin').delete()
    print("تم حذف المستخدم الإداري السابق")

# إنشاء مستخدم إداري جديد
admin_user = User.objects.create_superuser(
    username='admin',
    email='<EMAIL>',
    password='admin123',
    first_name='مدير',
    last_name='النظام'
)

print("تم إنشاء المستخدم الإداري بنجاح!")
print("اسم المستخدم: admin")
print("كلمة المرور: admin123")
print("البريد الإلكتروني: <EMAIL>")
