from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _

class UserGroup(models.Model):
    group_number = models.CharField(_('رقم المجموعة'), max_length=50, unique=True)
    group_name = models.CharField(_('اسم المجموعة'), max_length=100)
    notes = models.TextField(_('الملاحظات'), blank=True)

    class Meta:
        verbose_name = _('مجموعة المستخدمين')
        verbose_name_plural = _('مجموعات المستخدمين')

    def __str__(self):
        return f"{self.group_name} - {self.group_number}"

class Department(models.Model):
    department_number = models.CharField(_('رقم الدائرة'), max_length=50, unique=True)
    name = models.CharField(_('اسم الدائرة'), max_length=100)
    address = models.Char<PERSON><PERSON>(_('العنوان'), max_length=255)
    phone = models.CharField(_('رقم الهاتف'), max_length=20)
    email = models.EmailField(_('البريد الإلكتروني'))

    class Meta:
        verbose_name = _('الدائرة')
        verbose_name_plural = _('الدوائر')

    def __str__(self):
        return self.name

class User(AbstractUser):
    class AccountType(models.TextChoices):
        ADMIN = 'admin', _('مدير')
        USER = 'user', _('مستخدم')

    account_number = models.CharField(_('رقم الحساب'), max_length=50, unique=True)
    account_name = models.CharField(_('اسم الحساب'), max_length=100)
    account_type = models.CharField(
        _('نوع الحساب'),
        max_length=5,
        choices=AccountType.choices,
        default=AccountType.USER
    )
    department = models.ForeignKey(
        Department,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('الدائرة')
    )
    user_group = models.ForeignKey(
        UserGroup,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('مجموعة المستخدمين')
    )

    class Meta:
        verbose_name = _('المستخدم')
        verbose_name_plural = _('المستخدمون')

    def __str__(self):
        return f"{self.account_name} ({self.account_number})"
