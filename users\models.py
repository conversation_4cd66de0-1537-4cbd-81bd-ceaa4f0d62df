from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class UserGroup(models.Model):
    """مجموعات المستخدمين"""
    name = models.CharField(_('اسم المجموعة'), max_length=100, unique=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مجموعة المستخدمين')
        verbose_name_plural = _('مجموعات المستخدمين')
        db_table = 'user_groups'

    def __str__(self):
        return self.name


class CustomUser(AbstractUser):
    """نموذج المستخدم المخصص"""
    USER_TYPES = [
        ('admin', _('مدير')),
        ('user', _('مستخدم')),
    ]

    account_name = models.CharField(_('اسم الحساب'), max_length=100)
    user_type = models.CharField(_('نوع الحساب'), max_length=10, choices=USER_TYPES, default='user')
    department = models.CharField(_('الدائرة'), max_length=100, blank=True, null=True)
    user_group = models.ForeignKey(UserGroup, on_delete=models.SET_NULL, null=True, blank=True,
                                   verbose_name=_('مجموعة المستخدمين'))
    is_active_custom = models.BooleanField(_('نشط'), default=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('المستخدم')
        verbose_name_plural = _('المستخدمون')
        db_table = 'custom_users'

    def __str__(self):
        return f"{self.account_name} ({self.username})"


class AuditLog(models.Model):
    """سجل تدقيق العمليات"""
    ACTION_TYPES = [
        ('create', _('إنشاء')),
        ('update', _('تحديث')),
        ('delete', _('حذف')),
        ('login', _('تسجيل دخول')),
        ('logout', _('تسجيل خروج')),
    ]

    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, verbose_name=_('المستخدم'))
    action = models.CharField(_('نوع العملية'), max_length=20, choices=ACTION_TYPES)
    model_name = models.CharField(_('اسم النموذج'), max_length=100)
    object_id = models.CharField(_('معرف الكائن'), max_length=100, blank=True, null=True)
    changes = models.JSONField(_('التغييرات'), blank=True, null=True)
    ip_address = models.GenericIPAddressField(_('عنوان IP'), blank=True, null=True)
    user_agent = models.TextField(_('معلومات المتصفح'), blank=True, null=True)
    timestamp = models.DateTimeField(_('وقت العملية'), auto_now_add=True)

    class Meta:
        verbose_name = _('سجل التدقيق')
        verbose_name_plural = _('سجلات التدقيق')
        db_table = 'audit_logs'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.user.username} - {self.action} - {self.model_name} - {self.timestamp}"
