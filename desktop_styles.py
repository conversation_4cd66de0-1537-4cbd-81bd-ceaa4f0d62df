#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنماط تطبيق سطح المكتب
تحتوي على جميع الأنماط المستخدمة في التطبيق
"""

def get_main_stylesheet():
    """الحصول على الأنماط الرئيسية للتطبيق"""
    return """
        QMainWindow {
            background-color: #f5f6fa;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
        }
        
        /* القائمة الجانبية */
        QFrame#sidebar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
            border: none;
            border-right: 1px solid #e1e8ed;
        }
        
        QListWidget {
            background: transparent;
            border: none;
            outline: none;
            font-size: 14px;
            font-weight: 500;
        }
        
        QListWidget::item {
            color: white;
            padding: 15px 20px;
            border: none;
            margin: 2px 10px;
            border-radius: 8px;
            min-height: 20px;
        }
        
        QListWidget::item:selected {
            background-color: rgba(255, 255, 255, 0.25);
            color: white;
            font-weight: bold;
        }
        
        QListWidget::item:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }
        
        /* المحتوى الرئيسي */
        QStackedWidget {
            background-color: #f5f6fa;
            border: none;
        }
        
        /* شريط الحالة */
        QStatusBar {
            background-color: #ffffff;
            border-top: 1px solid #e1e8ed;
            color: #657786;
            font-size: 12px;
            padding: 8px 15px;
        }
        
        /* الإطارات */
        QFrame {
            border-radius: 8px;
        }
        
        /* التمرير */
        QScrollArea {
            border: none;
            background-color: transparent;
        }
        
        QScrollBar:vertical {
            background-color: #f1f3f4;
            width: 12px;
            border-radius: 6px;
            margin: 0;
        }
        
        QScrollBar::handle:vertical {
            background-color: #c1c8cd;
            border-radius: 6px;
            min-height: 20px;
            margin: 2px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #a8b2b8;
        }
        
        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            height: 0px;
        }
        
        /* الأزرار */
        QPushButton {
            font-weight: 500;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 13px;
        }
        
        QPushButton:hover {
            transform: translateY(-1px);
        }
        
        QPushButton:pressed {
            transform: translateY(0px);
        }
    """

def get_card_stylesheet():
    """أنماط البطاقات الإحصائية"""
    return """
        QFrame {
            background-color: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            padding: 0px;
        }
        
        QFrame:hover {
            border-color: #1da1f2;
            box-shadow: 0 4px 12px rgba(29, 161, 242, 0.15);
        }
    """

def get_activity_stylesheet():
    """أنماط قائمة الأنشطة"""
    return """
        QLabel {
            padding: 12px 16px;
            margin: 4px 0;
            background-color: #ffffff;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            font-size: 13px;
            color: #14171a;
        }
        
        QLabel:hover {
            background-color: #f7f9fa;
            border-color: #1da1f2;
        }
    """

def get_page_title_stylesheet():
    """أنماط عناوين الصفحات"""
    return """
        QLabel {
            font-size: 28px;
            font-weight: 700;
            color: #14171a;
            margin-bottom: 8px;
            padding: 0;
        }
    """

def get_page_description_stylesheet():
    """أنماط وصف الصفحات"""
    return """
        QLabel {
            font-size: 16px;
            color: #657786;
            margin-bottom: 24px;
            line-height: 1.4;
        }
    """

def get_sidebar_header_stylesheet():
    """أنماط رأس القائمة الجانبية"""
    return """
        QWidget {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0;
            margin: 0;
        }
        
        QLabel {
            color: white;
            font-size: 16px;
            font-weight: 600;
            background: transparent;
            text-align: center;
        }
    """

def get_user_info_stylesheet():
    """أنماط معلومات المستخدم"""
    return """
        QWidget {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 0;
            margin: 0;
        }
        
        QLabel {
            color: rgba(255, 255, 255, 0.9);
            font-size: 13px;
            background: transparent;
            font-weight: 400;
        }
    """

def get_development_notice_stylesheet():
    """أنماط إشعار قيد التطوير"""
    return """
        QLabel {
            font-size: 18px;
            color: #e67e22;
            padding: 30px;
            background-color: #fef5e7;
            border: 2px dashed #e67e22;
            border-radius: 12px;
            font-weight: 500;
        }
    """

def get_content_frame_stylesheet():
    """أنماط إطار المحتوى"""
    return """
        QFrame {
            background-color: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            padding: 0px;
        }
    """

def get_stat_value_stylesheet(color):
    """أنماط قيم الإحصائيات"""
    return f"""
        QLabel {{
            font-size: 24px;
            font-weight: 700;
            color: {color};
            background: transparent;
        }}
    """

def get_stat_title_stylesheet():
    """أنماط عناوين الإحصائيات"""
    return """
        QLabel {
            font-size: 14px;
            color: #657786;
            margin-top: 8px;
            background: transparent;
            font-weight: 500;
        }
    """

def get_stat_icon_stylesheet(color):
    """أنماط أيقونات الإحصائيات"""
    return f"""
        QLabel {{
            font-size: 28px;
            color: {color};
            background: transparent;
        }}
    """
