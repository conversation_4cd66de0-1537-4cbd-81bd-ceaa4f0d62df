from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView, DetailView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Count, Sum
from django.utils.translation import gettext_lazy as _

from .models import CustomUser, UserGroup, AuditLog
from employees.models import Employee
from payroll.models import Payroll, PayrollPeriod
from accounting.models import BankAccount


class DashboardView(LoginRequiredMixin, TemplateView):
    """لوحة المعلومات الرئيسية"""
    template_name = 'dashboard/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات عامة
        context['total_employees'] = Employee.objects.filter(status='active').count()
        context['total_users'] = CustomUser.objects.filter(is_active=True).count()
        context['total_bank_accounts'] = BankAccount.objects.filter(is_active=True).count()

        # إحصائيات الرواتب
        current_period = PayrollPeriod.objects.filter(is_closed=False).first()
        if current_period:
            context['current_period'] = current_period
            context['period_payrolls'] = Payroll.objects.filter(period=current_period).count()
            context['approved_payrolls'] = Payroll.objects.filter(
                period=current_period, status='approved'
            ).count()
            context['total_payroll_amount'] = Payroll.objects.filter(
                period=current_period, status__in=['approved', 'paid']
            ).aggregate(total=Sum('net_salary'))['total'] or 0

        # آخر العمليات
        context['recent_audit_logs'] = AuditLog.objects.select_related('user').order_by('-timestamp')[:10]

        return context


class UserListView(LoginRequiredMixin, ListView):
    """قائمة المستخدمين"""
    model = CustomUser
    template_name = 'users/user_list.html'
    context_object_name = 'users'
    paginate_by = 20

    def get_queryset(self):
        return CustomUser.objects.select_related('user_group').order_by('username')


class UserCreateView(LoginRequiredMixin, CreateView):
    """إنشاء مستخدم جديد"""
    model = CustomUser
    template_name = 'users/user_form.html'
    fields = ['username', 'account_name', 'email', 'user_type', 'department',
              'user_group', 'is_active_custom', 'phone']
    success_url = reverse_lazy('users:user_list')

    def form_valid(self, form):
        messages.success(self.request, _('تم إنشاء المستخدم بنجاح'))
        return super().form_valid(form)


class UserUpdateView(LoginRequiredMixin, UpdateView):
    """تحديث بيانات المستخدم"""
    model = CustomUser
    template_name = 'users/user_form.html'
    fields = ['account_name', 'email', 'user_type', 'department',
              'user_group', 'is_active_custom', 'phone']
    success_url = reverse_lazy('users:user_list')

    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث بيانات المستخدم بنجاح'))
        return super().form_valid(form)


class UserDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل المستخدم"""
    model = CustomUser
    template_name = 'users/user_detail.html'
    context_object_name = 'user_obj'


class UserDeleteView(LoginRequiredMixin, DeleteView):
    """حذف المستخدم"""
    model = CustomUser
    template_name = 'users/user_confirm_delete.html'
    success_url = reverse_lazy('users:user_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('تم حذف المستخدم بنجاح'))
        return super().delete(request, *args, **kwargs)


class UserGroupListView(LoginRequiredMixin, ListView):
    """قائمة مجموعات المستخدمين"""
    model = UserGroup
    template_name = 'users/group_list.html'
    context_object_name = 'groups'
    paginate_by = 20


class UserGroupCreateView(LoginRequiredMixin, CreateView):
    """إنشاء مجموعة مستخدمين جديدة"""
    model = UserGroup
    template_name = 'users/group_form.html'
    fields = ['name', 'notes']
    success_url = reverse_lazy('users:group_list')

    def form_valid(self, form):
        messages.success(self.request, _('تم إنشاء المجموعة بنجاح'))
        return super().form_valid(form)


class UserGroupUpdateView(LoginRequiredMixin, UpdateView):
    """تحديث مجموعة المستخدمين"""
    model = UserGroup
    template_name = 'users/group_form.html'
    fields = ['name', 'notes']
    success_url = reverse_lazy('users:group_list')

    def form_valid(self, form):
        messages.success(self.request, _('تم تحديث المجموعة بنجاح'))
        return super().form_valid(form)


class UserGroupDeleteView(LoginRequiredMixin, DeleteView):
    """حذف مجموعة المستخدمين"""
    model = UserGroup
    template_name = 'users/group_confirm_delete.html'
    success_url = reverse_lazy('users:group_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('تم حذف المجموعة بنجاح'))
        return super().delete(request, *args, **kwargs)


class AuditLogListView(LoginRequiredMixin, ListView):
    """قائمة سجلات التدقيق"""
    model = AuditLog
    template_name = 'users/audit_log_list.html'
    context_object_name = 'logs'
    paginate_by = 50

    def get_queryset(self):
        return AuditLog.objects.select_related('user').order_by('-timestamp')
