<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصيانة - المحاسب الشامل</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
        }
        
        body {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            font-family: 'Cairo', sans-serif;
        }
        
        .maintenance-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            padding: 3rem;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        
        .maintenance-icon {
            font-size: 5rem;
            color: var(--primary-color);
            animation: spin 4s linear infinite;
        }
        
        @keyframes spin {
            100% {
                transform: rotate(360deg);
            }
        }
        
        .progress {
            height: 10px;
            margin: 2rem 0;
        }
        
        .maintenance-title {
            font-size: 2rem;
            font-weight: bold;
            margin: 1.5rem 0;
            color: var(--primary-color);
        }
        
        .maintenance-text {
            color: #666;
            margin-bottom: 2rem;
        }
        
        .estimated-time {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            .maintenance-card {
                padding: 2rem;
            }
            
            .maintenance-icon {
                font-size: 4rem;
            }
            
            .maintenance-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-card">
        <i class="fas fa-cog maintenance-icon"></i>
        <h1 class="maintenance-title">النظام في وضع الصيانة</h1>
        <p class="maintenance-text">
            نقوم حالياً بإجراء بعض التحسينات على النظام لتقديم تجربة أفضل.
            <br>
            نعتذر عن الإزعاج وسنعود قريباً.
        </p>
        
        <div class="progress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                 role="progressbar" 
                 style="width: 75%">
            </div>
        </div>
        
        <div class="estimated-time">
            <i class="fas fa-clock me-2"></i>
            الوقت المتوقع للعودة: <strong>خلال ساعة</strong>
        </div>
        
        <div class="mt-4">
            <button onclick="window.location.reload()" class="btn btn-primary">
                <i class="fas fa-sync-alt me-2"></i>
                تحديث الصفحة
            </button>
        </div>
    </div>
</body>
</html>