# Generated by Django 4.2.7 on 2025-06-06 08:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="activity",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
                verbose_name="المستخدم",
            ),
        ),
        migrations.AddField(
            model_name="accountguide",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="core.accountguide",
                verbose_name="الحساب الرئيسي",
            ),
        ),
        migrations.AddIndex(
            model_name="activity",
            index=models.Index(
                fields=["-created_at"], name="core_activi_created_347311_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="activity",
            index=models.Index(
                fields=["user", "-created_at"], name="core_activi_user_id_5122a4_idx"
            ),
        ),
    ]
