{% extends 'base.html' %}
{% load i18n %}

{% block title %}لوحة التحكم - المحاسب الشامل{% endblock %}

{% block content %}
<div class="dashboard">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">لوحة التحكم</h1>
        <div class="date">{{ current_date|date:"Y-m-d" }}</div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <!-- Total Employees -->
        <div class="col-md-3">
            <div class="widget bg-primary text-white">
                <div class="widget-title">إجمالي الموظفين</div>
                <div class="widget-value">{{ total_employees }}</div>
                <div class="widget-link">
                    <a href="{% url 'employee_list' %}" class="text-white">
                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Total Salary -->
        <div class="col-md-3">
            <div class="widget bg-success text-white">
                <div class="widget-title">إجمالي الرواتب</div>
                <div class="widget-value">{{ total_salaries|floatformat:2 }}</div>
                <div class="widget-link">
                    <a href="{% url 'salary_list' %}" class="text-white">
                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Total Allowances -->
        <div class="col-md-3">
            <div class="widget bg-info text-white">
                <div class="widget-title">إجمالي المخصصات</div>
                <div class="widget-value">{{ total_allowances|floatformat:2 }}</div>
                <div class="widget-link">
                    <a href="{% url 'allowance_list' %}" class="text-white">
                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Total Deductions -->
        <div class="col-md-3">
            <div class="widget bg-warning text-white">
                <div class="widget-title">إجمالي الاستقطاعات</div>
                <div class="widget-value">{{ total_deductions|floatformat:2 }}</div>
                <div class="widget-link">
                    <a href="{% url 'deduction_list' %}" class="text-white">
                        عرض التفاصيل <i class="fas fa-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Employee Status Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">حالة الموظفين</h5>
                </div>
                <div class="card-body">
                    <canvas id="employeeStatusChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Monthly Salary Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الرواتب الشهرية</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlySalaryChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">آخر النشاطات</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>النشاط</th>
                            <th>المستخدم</th>
                            <th>التفاصيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for activity in recent_activities %}
                        <tr>
                            <td>{{ activity.created_at|date:"Y-m-d H:i" }}</td>
                            <td>{{ activity.action }}</td>
                            <td>{{ activity.user.get_full_name }}</td>
                            <td>{{ activity.details }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="text-center">لا توجد نشاطات حديثة</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Employee Status Chart
    const employeeStatusCtx = document.getElementById('employeeStatusChart').getContext('2d');
    new Chart(employeeStatusCtx, {
        type: 'pie',
        data: {
            labels: JSON.parse('{{ employee_status_labels|safe }}'),
            datasets: [{
                data: JSON.parse('{{ employee_status_data|safe }}'),
                backgroundColor: [
                    '#2ecc71',
                    '#e74c3c',
                    '#3498db',
                    '#f1c40f',
                    '#95a5a6'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Monthly Salary Chart
    const monthlySalaryCtx = document.getElementById('monthlySalaryChart').getContext('2d');
    new Chart(monthlySalaryCtx, {
        type: 'bar',
        data: {
            labels: JSON.parse('{{ monthly_salary_labels|safe }}'),
            datasets: [{
                label: 'إجمالي الرواتب',
                data: JSON.parse('{{ monthly_salary_data|safe }}'),
                backgroundColor: '#3498db'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
{% endblock %}