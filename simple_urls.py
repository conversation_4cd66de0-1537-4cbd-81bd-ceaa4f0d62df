"""
URLs مبسطة للتشغيل السريع
"""

from django.contrib import admin
from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import render
from django.contrib.auth.decorators import login_required

def home_view(request):
    """الصفحة الرئيسية"""
    return render(request, 'dashboard.html', {
        'total_employees': 150,
        'total_salaries': 2500000,
        'total_allowances': 350000,
        'total_deductions': 180000,
        'recent_activities': [
            {
                'action_type': 'إضافة موظف',
                'description': 'تم إضافة موظف جديد: أحمد محمد',
                'user': {'account_name': 'المدير'},
                'created_at': '2024-12-01 10:30:00'
            },
            {
                'action_type': 'معالجة راتب',
                'description': 'تم معالجة راتب شهر ديسمبر',
                'user': {'account_name': 'محاسب الرواتب'},
                'created_at': '2024-12-01 09:15:00'
            },
        ]
    })

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', home_view, name='home'),
    path('dashboard/', home_view, name='dashboard'),
]

# إضافة ملفات الوسائط في وضع التطوير
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
