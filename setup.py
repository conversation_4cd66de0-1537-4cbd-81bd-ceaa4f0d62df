#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النظام المحاسبي الموحد - ملف الإعداد
"""

from setuptools import setup, find_packages
import os

# قراءة README
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# قراءة المتطلبات
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="payroll-system",
    version="1.0.0",
    author="فريق التطوير",
    author_email="<EMAIL>",
    description="النظام المحاسبي الموحد لرواتب الموظفين",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/payroll-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Accounting",
        "Topic :: Office/Business :: Financial :: Point-Of-Sale",
        "Natural Language :: Arabic",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-django>=4.5.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "coverage>=6.0.0",
        ],
        "production": [
            "gunicorn>=20.1.0",
            "whitenoise>=6.0.0",
            "sentry-sdk>=1.5.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "payroll-system=run_system:main",
            "payroll-desktop=desktop_app:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.html", "*.css", "*.js", "*.png", "*.jpg", "*.ico"],
    },
    zip_safe=False,
    keywords="payroll accounting finance management arabic",
    project_urls={
        "Bug Reports": "https://github.com/your-repo/payroll-system/issues",
        "Source": "https://github.com/your-repo/payroll-system",
        "Documentation": "https://payroll-system.readthedocs.io/",
    },
)
