from django.urls import path
from . import views

app_name = 'employees'

urlpatterns = [
    # الموظفين
    path('', views.EmployeeListView.as_view(), name='employee_list'),
    path('create/', views.EmployeeCreateView.as_view(), name='employee_create'),
    path('<int:pk>/', views.EmployeeDetailView.as_view(), name='employee_detail'),
    path('<int:pk>/edit/', views.EmployeeUpdateView.as_view(), name='employee_edit'),
    path('<int:pk>/delete/', views.EmployeeDeleteView.as_view(), name='employee_delete'),
    
    # استيراد وتصدير
    path('import/', views.EmployeeImportView.as_view(), name='employee_import'),
    path('export/', views.EmployeeExportView.as_view(), name='employee_export'),
    
    # الأقسام
    path('departments/', views.DepartmentListView.as_view(), name='department_list'),
    path('departments/create/', views.DepartmentCreateView.as_view(), name='department_create'),
    path('departments/<int:pk>/edit/', views.DepartmentUpdateView.as_view(), name='department_edit'),
    path('departments/<int:pk>/delete/', views.DepartmentDeleteView.as_view(), name='department_delete'),
    
    # الشعب
    path('divisions/', views.DivisionListView.as_view(), name='division_list'),
    path('divisions/create/', views.DivisionCreateView.as_view(), name='division_create'),
    path('divisions/<int:pk>/edit/', views.DivisionUpdateView.as_view(), name='division_edit'),
    path('divisions/<int:pk>/delete/', views.DivisionDeleteView.as_view(), name='division_delete'),
    
    # العناوين الوظيفية
    path('job-titles/', views.JobTitleListView.as_view(), name='job_title_list'),
    path('job-titles/create/', views.JobTitleCreateView.as_view(), name='job_title_create'),
    path('job-titles/<int:pk>/edit/', views.JobTitleUpdateView.as_view(), name='job_title_edit'),
    path('job-titles/<int:pk>/delete/', views.JobTitleDeleteView.as_view(), name='job_title_delete'),
    
    # الشهادات
    path('certificates/', views.CertificateListView.as_view(), name='certificate_list'),
    path('certificates/create/', views.CertificateCreateView.as_view(), name='certificate_create'),
    path('certificates/<int:pk>/edit/', views.CertificateUpdateView.as_view(), name='certificate_edit'),
    path('certificates/<int:pk>/delete/', views.CertificateDeleteView.as_view(), name='certificate_delete'),
    
    # الدرجات الوظيفية
    path('job-grades/', views.JobGradeListView.as_view(), name='job_grade_list'),
    path('job-grades/create/', views.JobGradeCreateView.as_view(), name='job_grade_create'),
    path('job-grades/<int:pk>/edit/', views.JobGradeUpdateView.as_view(), name='job_grade_edit'),
    path('job-grades/<int:pk>/delete/', views.JobGradeDeleteView.as_view(), name='job_grade_delete'),
    
    # المراحل
    path('job-stages/', views.JobStageListView.as_view(), name='job_stage_list'),
    path('job-stages/create/', views.JobStageCreateView.as_view(), name='job_stage_create'),
    path('job-stages/<int:pk>/edit/', views.JobStageUpdateView.as_view(), name='job_stage_edit'),
    path('job-stages/<int:pk>/delete/', views.JobStageDeleteView.as_view(), name='job_stage_delete'),
]
