# سجل التغييرات - النظام المحاسبي الموحد

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور]

### مخطط له
- تطبيق الهاتف المحمول
- تكامل مع أنظمة الحضور والانصراف
- ذكاء اصطناعي للتحليلات المالية
- دعم لغات إضافية (الإنجليزية، الكردية)
- نظام الموافقات المتدرجة
- تقارير تفاعلية متقدمة

## [1.0.0] - 2024-12-05

### أضيف
- **النظام الأساسي**
  - إطار العمل Django 4.2.7 كأساس للنظام
  - واجهة سطح المكتب باستخدام PyQt5
  - واجهة ويب متجاوبة باستخدام Bootstrap 5
  - دعم اللغة العربية بالكامل

- **إدارة المستخدمين والأمان**
  - نظام مستخدمين مخصص مع مجموعات وصلاحيات
  - تشفير كلمات المرور باستخدام bcrypt
  - نظام تدقيق شامل (Audit Trails)
  - تسجيل دخول آمن مع حماية CSRF

- **إدارة الموظفين**
  - دليل شامل للأقسام والشعب
  - إدارة العناوين الوظيفية والشهادات
  - نظام الدرجات الوظيفية والمراحل
  - استيراد وتصدير بيانات الموظفين من/إلى Excel
  - تتبع تاريخ التوظيف وحالة الموظف

- **النظام المحاسبي**
  - دليل حسابات قابل للتخصيص (شجرة حسابات)
  - إدارة العملات المتعددة مع أسعار الصرف
  - إدارة الفترات المحاسبية
  - دليل الصناديق والمصارف
  - الحسابات البنكية المتخصصة:
    - حساب التشغيلية
    - حساب الخزينة الموحد (الرواتب)
  - سندات الصرف والقبض
  - قيود اليومية التلقائية
  - مراكز التكلفة لتحليل التكاليف

- **نظام الرواتب**
  - حساب الرواتب الأساسية والمخصصات
  - المخصصات المدعومة:
    - مخصصات المنصب
    - مخصصات الزوجية
    - مخصصات الأولاد
    - مخصصات هندسية
    - مخصصات الشهادة
    - مخصصات الحرفة
    - مخصصات الخطورة
    - مخصصات الموقع الجغرافي
  - الاستقطاعات التلقائية:
    - صندوق تقاعد موظفي الدولة (10%)
    - حصة الدائرة في المساهمة الحكومية (15%)
    - ضريبة الدخل (حسب الشرائح)
    - صندوق هيئة الحماية الاجتماعية
    - أمانات الضمان الصحي
    - أقساط المصارف
  - شرائح ضريبية قابلة للتهيئة
  - ربط تلقائي مع النظام المحاسبي
  - مسيرات رواتب شهرية

- **التقارير والتحليلات**
  - تقارير مالية أساسية:
    - تقرير اليومية العامة
    - كشف حساب
    - تقرير أرصدة الحسابات
    - تقرير سندات الصرف والقبض
  - تقارير الرواتب:
    - مسير الرواتب الشهري
    - كشوف الرواتب الفردية
    - تقارير ملخص الرواتب
    - تقارير الضرائب والتأمينات
  - لوحة معلومات تفاعلية
  - تصدير إلى PDF و Excel
  - قوالب تقارير قابلة للتخصيص

- **المهام الخلفية والأتمتة**
  - نظام Celery للمهام الخلفية
  - نسخ احتياطية تلقائية يومية
  - تنظيف الملفات المؤقتة
  - حساب الرواتب الجماعي
  - إرسال الإشعارات عبر البريد الإلكتروني

- **الأمان والأداء**
  - حماية HTTPS مع شهادات SSL
  - ضغط الملفات والاستجابات
  - تخزين مؤقت باستخدام Redis
  - فهرسة قاعدة البيانات المحسنة
  - حماية من هجمات XSS و CSRF

- **النشر والتشغيل**
  - دعم Docker و Docker Compose
  - إعدادات منفصلة للتطوير والإنتاج
  - خادم Nginx للملفات الثابتة
  - قاعدة بيانات PostgreSQL للإنتاج
  - مراقبة الأخطاء باستخدام Sentry

- **أدوات التطوير**
  - أوامر Django مخصصة لإعداد البيانات الأولية
  - ملف تشغيل سريع (run_system.py)
  - ملف batch للويندوز (start_system.bat)
  - وثائق شاملة باللغة العربية

### تم تحسينه
- أداء الاستعلامات مع الفهرسة المناسبة
- واجهة المستخدم متجاوبة لجميع الأجهزة
- تجربة المستخدم محسنة مع رسائل واضحة
- أمان محسن مع تشفير البيانات الحساسة

### أمان
- تشفير كلمات المرور باستخدام bcrypt
- حماية CSRF في جميع النماذج
- تدقيق شامل لجميع العمليات
- صلاحيات مستخدمين متدرجة
- حماية من حقن SQL
- تشفير الاتصالات باستخدام HTTPS

## [0.1.0] - 2024-11-01

### أضيف
- إعداد المشروع الأولي
- هيكل قاعدة البيانات الأساسي
- نماذج Django الأولية
- واجهة إدارة Django الأساسية

---

## أنواع التغييرات

- `أضيف` للميزات الجديدة
- `تم تغييره` للتغييرات في الميزات الموجودة
- `مهجور` للميزات التي ستتم إزالتها قريباً
- `تم إزالته` للميزات المحذوفة
- `تم إصلاحه` لإصلاح الأخطاء
- `أمان` في حالة الثغرات الأمنية

## روابط المقارنة

- [غير منشور](https://github.com/your-repo/payroll-system/compare/v1.0.0...HEAD)
- [1.0.0](https://github.com/your-repo/payroll-system/compare/v0.1.0...v1.0.0)
- [0.1.0](https://github.com/your-repo/payroll-system/releases/tag/v0.1.0)
