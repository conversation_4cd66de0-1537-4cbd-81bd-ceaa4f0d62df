from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, UserGroup  # AuditLog معطل مؤقتاً


@admin.register(UserGroup)
class UserGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_at', 'updated_at']
    search_fields = ['name']
    list_filter = ['created_at']
    ordering = ['name']


@admin.register(User)
class UserAdmin(UserAdmin):
    list_display = ['username', 'account_name', 'account_type', 'department', 'is_active', 'date_joined']
    list_filter = ['account_type', 'is_active', 'date_joined', 'user_group']
    search_fields = ['username', 'account_name', 'email']
    ordering = ['username']

    fieldsets = UserAdmin.fieldsets + (
        (_('معلومات إضافية'), {
            'fields': ('account_name', 'account_type', 'department', 'user_group')
        }),
    )

    add_fieldsets = UserAdmin.add_fieldsets + (
        (_('معلومات إضافية'), {
            'fields': ('account_name', 'account_type', 'department', 'user_group')
        }),
    )

# AuditLog معطل مؤقتاً
# @admin.register(AuditLog)
# class AuditLogAdmin(admin.ModelAdmin):
#     ...
