from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.conf import settings
import calendar
import json
import os
import arabic_reshaper
from bidi.algorithm import get_display
import logging

logger = logging.getLogger(__name__)

def generate_unique_number(model, field_name, prefix='', length=6):
    """توليد رقم فريد للسجلات"""
    from django.db.models import Max
    max_number = model.objects.aggregate(Max(field_name))[f'{field_name}__max']
    
    if not max_number:
        return f"{prefix}{str(1).zfill(length)}"
    
    try:
        # استخراج الرقم من آخر قيمة
        current_number = int(''.join(filter(str.isdigit, max_number)))
        next_number = current_number + 1
        return f"{prefix}{str(next_number).zfill(length)}"
    except (ValueError, TypeError):
        return f"{prefix}{str(1).zfill(length)}"

def format_currency(amount):
    """تنسيق المبالغ المالية"""
    try:
        return "{:,.3f}".format(float(amount))
    except (ValueError, TypeError):
        return "0.000"

def get_arabic_month_name(month_number):
    """الحصول على اسم الشهر باللغة العربية"""
    arabic_months = {
        1: 'كانون الثاني',
        2: 'شباط',
        3: 'آذار',
        4: 'نيسان',
        5: 'أيار',
        6: 'حزيران',
        7: 'تموز',
        8: 'آب',
        9: 'أيلول',
        10: 'تشرين الأول',
        11: 'تشرين الثاني',
        12: 'كانون الأول'
    }
    return arabic_months.get(month_number, '')

def get_month_days(year, month):
    """الحصول على عدد أيام الشهر"""
    return calendar.monthrange(year, month)[1]

def format_arabic_text(text):
    """تنسيق النص العربي للطباعة"""
    try:
        reshaped_text = arabic_reshaper.reshape(str(text))
        return get_display(reshaped_text)
    except Exception as e:
        logger.error(f"Error formatting Arabic text: {e}")
        return text

def validate_file_size(value):
    """التحقق من حجم الملف"""
    filesize = value.size
    max_size = settings.FILE_UPLOAD_MAX_MEMORY_SIZE
    
    if filesize > max_size:
        raise ValidationError(
            _('الحد الأقصى لحجم الملف هو %(max_size)s. الحجم الحالي هو %(size)s') % {
                'max_size': filesizeformat(max_size),
                'size': filesizeformat(filesize)
            }
        )

def create_backup(backup_name=None):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    from django.core.management import call_command
    
    try:
        if not backup_name:
            backup_name = f'backup_{timezone.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        backup_path = os.path.join(settings.BACKUP_DIR, backup_name)
        
        with open(backup_path, 'w') as f:
            call_command('dumpdata', stdout=f)
        
        return backup_path
    
    except Exception as e:
        logger.error(f"Error creating backup: {e}")
        raise

def restore_backup(backup_file):
    """استعادة نسخة احتياطية"""
    from django.core.management import call_command
    
    try:
        call_command('loaddata', backup_file)
        return True
    
    except Exception as e:
        logger.error(f"Error restoring backup: {e}")
        raise

def calculate_financial_periods(year):
    """حساب الفترات المالية للسنة"""
    periods = []
    current_date = timezone.now().date()
    
    for month in range(1, 13):
        start_date = timezone.datetime(year, month, 1).date()
        end_date = start_date.replace(
            day=calendar.monthrange(year, month)[1]
        )
        
        periods.append({
            'month': month,
            'month_name': get_arabic_month_name(month),
            'start_date': start_date,
            'end_date': end_date,
            'is_current': (
                current_date.year == year and 
                current_date.month == month
            ),
            'is_future': (
                start_date > current_date
            ),
            'is_past': (
                end_date < current_date
            )
        })
    
    return periods

def get_system_info():
    """الحصول على معلومات النظام"""
    import psutil
    
    info = {
        'cpu_usage': psutil.cpu_percent(interval=1),
        'memory': psutil.virtual_memory()._asdict(),
        'disk': psutil.disk_usage('/')._asdict(),
        'boot_time': psutil.boot_time(),
    }
    
    # حجم قاعدة البيانات والملفات
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(settings.MEDIA_ROOT):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            total_size += os.path.getsize(fp)
    
    info['media_size'] = total_size
    
    return info

def format_phone_number(phone):
    """تنسيق رقم الهاتف"""
    if not phone:
        return ''
    
    # إزالة كل شيء ما عدا الأرقام
    numbers = ''.join(filter(str.isdigit, str(phone)))
    
    if len(numbers) == 11:  # رقم عراقي
        return f"{numbers[:4]} {numbers[4:7]} {numbers[7:]}"
    
    return numbers

def calculate_age(birth_date):
    """حساب العمر"""
    if not birth_date:
        return 0
    
    today = timezone.now().date()
    return (
        today.year - birth_date.year - 
        ((today.month, today.day) < (birth_date.month, birth_date.day))
    )