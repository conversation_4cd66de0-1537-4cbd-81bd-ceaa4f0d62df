{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة المعلومات - النظام المحاسبي الموحد{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">لوحة المعلومات</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-tachometer-alt me-2"></i>
            لوحة المعلومات
        </h1>
        <div class="text-muted">
            <i class="fas fa-calendar me-1"></i>
            {{ "now"|date:"Y/m/d H:i" }}
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <!-- إجمالي الموظفين -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الموظفين النشطين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_employees|default:0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجمالي المستخدمين -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المستخدمون النشطون
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_users|default:0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-cog fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الحسابات البنكية -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                الحسابات البنكية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_bank_accounts|default:0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-university fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجمالي الرواتب -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                إجمالي الرواتب (د.ع)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_payroll_amount|floatformat:0|default:0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات الفترة الحالية -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-alt me-2"></i>
                        الفترة المحاسبية الحالية
                    </h6>
                </div>
                <div class="card-body">
                    {% if current_period %}
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>اسم الفترة:</strong> {{ current_period.name }}</p>
                                <p><strong>السنة المالية:</strong> {{ current_period.year }}</p>
                                <p><strong>الشهر:</strong> {{ current_period.month }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>مسيرات الرواتب:</strong> {{ period_payrolls|default:0 }}</p>
                                <p><strong>المعتمدة:</strong> {{ approved_payrolls|default:0 }}</p>
                                <p><strong>الحالة:</strong> 
                                    {% if current_period.is_closed %}
                                        <span class="badge bg-danger">مقفلة</span>
                                    {% else %}
                                        <span class="badge bg-success">مفتوحة</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        
                        {% if not current_period.is_closed %}
                            <div class="progress mb-3">
                                {% widthratio approved_payrolls period_payrolls 100 as progress_percent %}
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ progress_percent|default:0 }}%"
                                     aria-valuenow="{{ progress_percent|default:0 }}" 
                                     aria-valuemin="0" aria-valuemax="100">
                                    {{ progress_percent|default:0 }}%
                                </div>
                            </div>
                        {% endif %}
                        
                        <div class="text-center">
                            <a href="{% url 'payroll:payroll_list' %}" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>
                                عرض مسيرات الرواتب
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                            <p>لا توجد فترة محاسبية نشطة حالياً</p>
                            <a href="{% url 'payroll:period_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إنشاء فترة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- الروابط السريعة -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        الروابط السريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="{% url 'employees:employee_create' %}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة موظف جديد
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{% url 'payroll:payroll_create' %}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-calculator me-2"></i>
                                حساب راتب
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{% url 'accounting:bank_account_list' %}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-university me-2"></i>
                                الحسابات البنكية
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{% url 'reports:template_list' %}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر العمليات -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        آخر العمليات
                    </h6>
                    <a href="{% url 'users:audit_log_list' %}" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_audit_logs %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>العملية</th>
                                        <th>النموذج</th>
                                        <th>التوقيت</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for log in recent_audit_logs %}
                                        <tr>
                                            <td>
                                                <i class="fas fa-user me-1"></i>
                                                {{ log.user.account_name }}
                                            </td>
                                            <td>
                                                {% if log.action == 'create' %}
                                                    <span class="badge bg-success">إنشاء</span>
                                                {% elif log.action == 'update' %}
                                                    <span class="badge bg-warning">تحديث</span>
                                                {% elif log.action == 'delete' %}
                                                    <span class="badge bg-danger">حذف</span>
                                                {% else %}
                                                    <span class="badge bg-info">{{ log.get_action_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ log.model_name }}</td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ log.timestamp|timesince }} مضت
                                                </small>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>لا توجد عمليات حديثة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .btn-block {
        display: block;
        width: 100%;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث الوقت كل دقيقة
    setInterval(function() {
        location.reload();
    }, 60000);
    
    // رسوم بيانية بسيطة (يمكن تطويرها لاحقاً)
    console.log('لوحة المعلومات محملة بنجاح');
</script>
{% endblock %}
