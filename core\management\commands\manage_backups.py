from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.conf import settings
from core.activity_models import SystemLog
import os
import shutil
import glob
import json

class Command(BaseCommand):
    help = 'إدارة النسخ الاحتياطية للنظام'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            type=str,
            choices=['create', 'restore', 'list', 'clean'],
            help='الإجراء المطلوب تنفيذه'
        )
        parser.add_argument(
            '--file',
            type=str,
            help='مسار ملف النسخة الاحتياطية للاستعادة'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='عدد الأيام للاحتفاظ بالنسخ الاحتياطية'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        try:
            if action == 'create':
                self.create_backup()
            elif action == 'restore':
                if not options['file']:
                    raise CommandError('يجب تحديد ملف النسخة الاحتياطية للاستعادة')
                self.restore_backup(options['file'])
            elif action == 'list':
                self.list_backups()
            elif action == 'clean':
                self.clean_old_backups(options['days'])
                
        except Exception as e:
            SystemLog.error(f'خطأ في إدارة النسخ الاحتياطية: {action}', str(e))
            raise CommandError(str(e))

    def create_backup(self):
        """إنشاء نسخة احتياطية جديدة"""
        from django.core.management import call_command
        
        # إنشاء اسم الملف بالتاريخ والوقت
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        backup_path = os.path.join(settings.BACKUP_DIR, f'backup_{timestamp}')
        
        try:
            # إنشاء مجلد النسخة الاحتياطية
            os.makedirs(backup_path, exist_ok=True)
            
            # حفظ بيانات قاعدة البيانات
            db_file = os.path.join(backup_path, 'database.json')
            with open(db_file, 'w') as f:
                call_command('dumpdata', stdout=f, indent=2)
            
            # نسخ ملفات الوسائط
            media_backup = os.path.join(backup_path, 'media')
            if os.path.exists(settings.MEDIA_ROOT):
                shutil.copytree(settings.MEDIA_ROOT, media_backup)
            
            # حفظ معلومات النسخة الاحتياطية
            info = {
                'timestamp': timestamp,
                'created_at': timezone.now().isoformat(),
                'django_version': settings.DJANGO_VERSION,
                'backup_type': 'full',
            }
            
            with open(os.path.join(backup_path, 'backup_info.json'), 'w') as f:
                json.dump(info, f, indent=2, ensure_ascii=False)
            
            # ضغط المجلد
            shutil.make_archive(backup_path, 'zip', backup_path)
            shutil.rmtree(backup_path)  # حذف المجلد الأصلي
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'تم إنشاء النسخة الاحتياطية بنجاح: backup_{timestamp}.zip'
                )
            )
            SystemLog.info(f'تم إنشاء نسخة احتياطية: backup_{timestamp}.zip')
            
        except Exception as e:
            if os.path.exists(backup_path):
                shutil.rmtree(backup_path)
            raise CommandError(f'فشل إنشاء النسخة الاحتياطية: {str(e)}')

    def restore_backup(self, backup_file):
        """استعادة نسخة احتياطية"""
        from django.core.management import call_command
        
        if not os.path.exists(backup_file):
            raise CommandError(f'ملف النسخة الاحتياطية غير موجود: {backup_file}')
        
        # إنشاء مجلد مؤقت للاستعادة
        temp_dir = os.path.join(settings.TEMP_DIR, 'restore_temp')
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        
        try:
            # فك ضغط الملف
            shutil.unpack_archive(backup_file, temp_dir, 'zip')
            
            # استعادة قاعدة البيانات
            db_file = os.path.join(temp_dir, 'database.json')
            call_command('loaddata', db_file)
            
            # استعادة ملفات الوسائط
            media_backup = os.path.join(temp_dir, 'media')
            if os.path.exists(media_backup):
                if os.path.exists(settings.MEDIA_ROOT):
                    shutil.rmtree(settings.MEDIA_ROOT)
                shutil.copytree(media_backup, settings.MEDIA_ROOT)
            
            self.stdout.write(
                self.style.SUCCESS('تم استعادة النسخة الاحتياطية بنجاح')
            )
            SystemLog.info(f'تم استعادة النسخة الاحتياطية: {backup_file}')
            
        except Exception as e:
            raise CommandError(f'فشل استعادة النسخة الاحتياطية: {str(e)}')
        
        finally:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

    def list_backups(self):
        """عرض قائمة النسخ الاحتياطية"""
        backups = glob.glob(os.path.join(settings.BACKUP_DIR, 'backup_*.zip'))
        
        if not backups:
            self.stdout.write('لا توجد نسخ احتياطية')
            return
        
        self.stdout.write('النسخ الاحتياطية المتوفرة:')
        for backup in sorted(backups, reverse=True):
            size = os.path.getsize(backup) / (1024 * 1024)  # تحويل إلى ميجابايت
            created = timezone.datetime.fromtimestamp(
                os.path.getctime(backup)
            ).strftime('%Y-%m-%d %H:%M:%S')
            
            self.stdout.write(
                f'- {os.path.basename(backup)}'
                f' (الحجم: {size:.2f} MB,'
                f' تاريخ الإنشاء: {created})'
            )

    def clean_old_backups(self, days):
        """حذف النسخ الاحتياطية القديمة"""
        cutoff = timezone.now() - timezone.timedelta(days=days)
        backups = glob.glob(os.path.join(settings.BACKUP_DIR, 'backup_*.zip'))
        
        deleted = 0
        for backup in backups:
            created = timezone.datetime.fromtimestamp(os.path.getctime(backup))
            if created < cutoff:
                os.remove(backup)
                deleted += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'تم حذف {deleted} نسخة احتياطية قديمة'
            )
        )
        SystemLog.info(f'تم حذف {deleted} نسخة احتياطية قديمة')