from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from employees.models import Employee
from accounting.models import BankAccount, CostCenter


class AllowanceType(models.Model):
    """أنواع المخصصات"""
    name = models.CharField(_('اسم المخصص'), max_length=100, unique=True)
    code = models.CharField(_('رمز المخصص'), max_length=10, unique=True)
    is_percentage = models.BooleanField(_('نسبة مئوية'), default=False)
    default_amount = models.DecimalField(_('المبلغ الافتراضي'), max_digits=10, decimal_places=2, default=0.00)
    is_taxable = models.BooleanField(_('خاضع للضريبة'), default=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('نوع المخصص')
        verbose_name_plural = _('أنواع المخصصات')
        db_table = 'allowance_types'

    def __str__(self):
        return self.name


class DeductionType(models.Model):
    """أنواع الاستقطاعات"""
    name = models.CharField(_('اسم الاستقطاع'), max_length=100, unique=True)
    code = models.CharField(_('رمز الاستقطاع'), max_length=10, unique=True)
    is_percentage = models.BooleanField(_('نسبة مئوية'), default=False)
    default_amount = models.DecimalField(_('المبلغ الافتراضي'), max_digits=10, decimal_places=2, default=0.00)
    is_mandatory = models.BooleanField(_('إجباري'), default=False)
    is_active = models.BooleanField(_('نشط'), default=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('نوع الاستقطاع')
        verbose_name_plural = _('أنواع الاستقطاعات')
        db_table = 'deduction_types'

    def __str__(self):
        return self.name


class TaxBracket(models.Model):
    """شرائح الضريبة"""
    name = models.CharField(_('اسم الشريحة'), max_length=100)
    min_amount = models.DecimalField(_('الحد الأدنى'), max_digits=12, decimal_places=2)
    max_amount = models.DecimalField(_('الحد الأعلى'), max_digits=12, decimal_places=2, null=True, blank=True)
    tax_rate = models.DecimalField(_('معدل الضريبة %'), max_digits=5, decimal_places=2,
                                  validators=[MinValueValidator(0), MaxValueValidator(100)])
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('شريحة الضريبة')
        verbose_name_plural = _('شرائح الضريبة')
        db_table = 'tax_brackets'
        ordering = ['min_amount']

    def __str__(self):
        return f"{self.name} - {self.tax_rate}%"


class PayrollPeriod(models.Model):
    """فترات الرواتب"""
    name = models.CharField(_('اسم الفترة'), max_length=100)
    year = models.IntegerField(_('السنة'))
    month = models.IntegerField(_('الشهر'), validators=[MinValueValidator(1), MaxValueValidator(12)])
    start_date = models.DateField(_('تاريخ البداية'))
    end_date = models.DateField(_('تاريخ النهاية'))
    is_closed = models.BooleanField(_('مقفلة'), default=False)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('فترة الرواتب')
        verbose_name_plural = _('فترات الرواتب')
        db_table = 'payroll_periods'
        unique_together = ['year', 'month']
        ordering = ['-year', '-month']

    def __str__(self):
        return f"{self.name} - {self.year}/{self.month:02d}"


class Payroll(models.Model):
    """مسير الرواتب"""
    STATUS_CHOICES = [
        ('draft', _('مسودة')),
        ('calculated', _('محسوب')),
        ('approved', _('معتمد')),
        ('paid', _('مدفوع')),
        ('cancelled', _('ملغي')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.PROTECT, verbose_name=_('الموظف'))
    period = models.ForeignKey(PayrollPeriod, on_delete=models.PROTECT, verbose_name=_('فترة الرواتب'))

    # مكونات الراتب الأساسية
    basic_salary = models.DecimalField(_('الراتب الأساسي'), max_digits=12, decimal_places=2, default=0.00)
    salary_difference = models.DecimalField(_('فرق راتب'), max_digits=12, decimal_places=2, default=0.00)

    # المخصصات
    position_allowance = models.DecimalField(_('مخصصات المنصب'), max_digits=10, decimal_places=2, default=0.00)
    marriage_allowance = models.DecimalField(_('مخصصات الزوجية'), max_digits=10, decimal_places=2, default=0.00)
    children_allowance = models.DecimalField(_('مخصصات الأولاد'), max_digits=10, decimal_places=2, default=0.00)
    engineering_allowance = models.DecimalField(_('مخصصات هندسية'), max_digits=10, decimal_places=2, default=0.00)
    certificate_allowance = models.DecimalField(_('مخصصات الشهادة'), max_digits=10, decimal_places=2, default=0.00)
    craft_allowance = models.DecimalField(_('مخصصات الحرفة'), max_digits=10, decimal_places=2, default=0.00)
    danger_allowance = models.DecimalField(_('مخصصات الخطورة'), max_digits=10, decimal_places=2, default=0.00)
    location_allowance = models.DecimalField(_('مخصصات الموقع الجغرافي'), max_digits=10, decimal_places=2, default=0.00)
    total_allowances = models.DecimalField(_('إجمالي المخصصات'), max_digits=12, decimal_places=2, default=0.00)

    # الاستقطاعات
    retirement_fund = models.DecimalField(_('صندوق تقاعد موظفي الدولة'), max_digits=10, decimal_places=2, default=0.00)
    government_contribution = models.DecimalField(_('حصة الدائرة في المساهمة الحكومية'), max_digits=10, decimal_places=2, default=0.00)
    income_tax = models.DecimalField(_('ضريبة الدخل'), max_digits=10, decimal_places=2, default=0.00)
    social_protection_fund = models.DecimalField(_('صندوق هيئة الحماية الاجتماعية'), max_digits=10, decimal_places=2, default=0.00)
    health_insurance = models.DecimalField(_('أمانات الضمان الصحي'), max_digits=10, decimal_places=2, default=0.00)
    other_departments = models.DecimalField(_('دوائر وجهات أخرى'), max_digits=10, decimal_places=2, default=0.00)
    salary_difference_deduction = models.DecimalField(_('فرق راتب (استقطاع)'), max_digits=10, decimal_places=2, default=0.00)
    execution_departments = models.DecimalField(_('دوائر التنفيذ'), max_digits=10, decimal_places=2, default=0.00)
    bank_installments = models.DecimalField(_('أقساط المصارف'), max_digits=10, decimal_places=2, default=0.00)
    total_deductions = models.DecimalField(_('إجمالي الاستقطاعات'), max_digits=12, decimal_places=2, default=0.00)

    # الصافي
    net_salary = models.DecimalField(_('صافي الراتب'), max_digits=12, decimal_places=2, default=0.00)

    # معلومات إضافية
    status = models.CharField(_('الحالة'), max_length=15, choices=STATUS_CHOICES, default='draft')
    bank_account = models.ForeignKey(BankAccount, on_delete=models.PROTECT, verbose_name=_('الحساب البنكي'),
                                    null=True, blank=True)
    cost_center = models.ForeignKey(CostCenter, on_delete=models.PROTECT, verbose_name=_('مركز التكلفة'),
                                   null=True, blank=True)
    notes = models.TextField(_('الملاحظات'), blank=True, null=True)

    # تواريخ النظام
    calculated_at = models.DateTimeField(_('تاريخ الحساب'), null=True, blank=True)
    approved_at = models.DateTimeField(_('تاريخ الاعتماد'), null=True, blank=True)
    paid_at = models.DateTimeField(_('تاريخ الدفع'), null=True, blank=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('مسير الرواتب')
        verbose_name_plural = _('مسيرات الرواتب')
        db_table = 'payrolls'
        unique_together = ['employee', 'period']
        ordering = ['-period__year', '-period__month', 'employee__employee_number']

    def __str__(self):
        return f"{self.employee.full_name} - {self.period}"

    def calculate_totals(self):
        """حساب الإجماليات"""
        # حساب إجمالي المخصصات
        self.total_allowances = (
            self.position_allowance + self.marriage_allowance + self.children_allowance +
            self.engineering_allowance + self.certificate_allowance + self.craft_allowance +
            self.danger_allowance + self.location_allowance
        )

        # حساب إجمالي الاستقطاعات
        self.total_deductions = (
            self.retirement_fund + self.government_contribution + self.income_tax +
            self.social_protection_fund + self.health_insurance + self.other_departments +
            self.salary_difference_deduction + self.execution_departments + self.bank_installments
        )

        # حساب صافي الراتب
        gross_salary = self.basic_salary + self.salary_difference + self.total_allowances
        self.net_salary = gross_salary - self.total_deductions

        return self.net_salary

    def calculate_automatic_deductions(self):
        """حساب الاستقطاعات التلقائية"""
        # صندوق التقاعد (10% من الراتب الأساسي)
        self.retirement_fund = self.basic_salary * Decimal('0.10')

        # حصة الدائرة (15% من الراتب الأساسي)
        self.government_contribution = self.basic_salary * Decimal('0.15')

        # حساب ضريبة الدخل حسب الشرائح
        self.income_tax = self.calculate_income_tax()

    def calculate_income_tax(self):
        """حساب ضريبة الدخل حسب الشرائح"""
        taxable_income = self.basic_salary + self.total_allowances
        tax_amount = Decimal('0.00')

        tax_brackets = TaxBracket.objects.filter(is_active=True).order_by('min_amount')

        for bracket in tax_brackets:
            if taxable_income > bracket.min_amount:
                if bracket.max_amount and taxable_income > bracket.max_amount:
                    bracket_income = bracket.max_amount - bracket.min_amount
                else:
                    bracket_income = taxable_income - bracket.min_amount

                tax_amount += bracket_income * (bracket.tax_rate / 100)

                if bracket.max_amount and taxable_income <= bracket.max_amount:
                    break

        return tax_amount
