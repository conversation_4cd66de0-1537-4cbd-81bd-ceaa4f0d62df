#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إصلاح النظام الشاملة
تقوم بإصلاح جميع المشاكل المكتشفة في النظام
"""

import subprocess
import sys
import os
from pathlib import Path

def print_banner():
    """طباعة شعار الإصلاح"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           🔧 أداة إصلاح النظام الشاملة                       ║
    ║                                                              ║
    ║                  نظام إدارة الرواتب والمحاسبة               ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_and_install_requirements():
    """فحص وتثبيت المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    requirements = {
        'Django': 'Django>=4.2,<5.0',
        'PyQt5': 'PyQt5>=5.15.9',
        'pandas': 'pandas>=2.0.2',
        'openpyxl': 'openpyxl>=3.1.2',
        'psycopg2': 'psycopg2-binary>=2.9.5'
    }
    
    missing = []
    
    for package, pip_name in requirements.items():
        try:
            if package == 'psycopg2':
                __import__('psycopg2')
            else:
                __import__(package.lower())
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - مفقود")
            missing.append(pip_name)
    
    if missing:
        print(f"\n📦 تثبيت المتطلبات المفقودة...")
        for package in missing:
            try:
                print(f"   تثبيت {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"   ✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"   ❌ فشل تثبيت {package}")
    
    return len(missing) == 0

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n🗄️ إعداد قاعدة البيانات...")
    
    try:
        # تشغيل migrations
        print("   تشغيل migrations...")
        subprocess.run([sys.executable, 'manage.py', 'makemigrations'], 
                      capture_output=True, check=True)
        subprocess.run([sys.executable, 'manage.py', 'migrate'], 
                      capture_output=True, check=True)
        print("   ✅ تم إعداد قاعدة البيانات")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def create_superuser():
    """إنشاء مستخدم إداري"""
    print("\n👤 إنشاء المستخدم الإداري...")
    
    script = """
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'payroll_system.settings')
django.setup()

from users.models import CustomUser
from django.contrib.auth.hashers import make_password

# حذف أي مستخدم admin موجود
CustomUser.objects.filter(username='admin').delete()

# إنشاء مستخدم جديد
user = CustomUser.objects.create(
    username='admin',
    email='<EMAIL>',
    account_name='المدير العام',
    password=make_password('admin123'),
    is_staff=True,
    is_superuser=True,
    is_active=True
)

print('تم إنشاء المستخدم الإداري بنجاح')
print('اسم المستخدم: admin')
print('كلمة المرور: admin123')
"""
    
    try:
        result = subprocess.run([sys.executable, '-c', script], 
                              capture_output=True, text=True, check=True)
        print("   ✅ تم إنشاء المستخدم الإداري")
        print("   📝 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ خطأ في إنشاء المستخدم: {e}")
        return False

def fix_celery_issue():
    """إصلاح مشكلة Celery"""
    print("\n🔧 إصلاح مشكلة Celery...")

    try:
        # تعطيل استيراد Celery في __init__.py
        init_file = 'payroll_system/__init__.py'
        if os.path.exists(init_file):
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()

            if 'from .celery import app as celery_app' in content:
                content = content.replace(
                    'from .celery import app as celery_app',
                    '# from .celery import app as celery_app  # معطل مؤقتاً'
                )
                content = content.replace(
                    "__all__ = ('celery_app',)",
                    "# __all__ = ('celery_app',)  # معطل مؤقتاً"
                )

                with open(init_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                print("   ✅ تم تعطيل Celery في __init__.py")

        return True
    except Exception as e:
        print(f"   ❌ خطأ في إصلاح Celery: {e}")
        return False

def test_desktop_app():
    """اختبار تطبيق سطح المكتب"""
    print("\n🖥️ اختبار تطبيق سطح المكتب...")

    try:
        # اختبار استيراد PyQt5
        from PyQt5.QtWidgets import QApplication
        print("   ✅ PyQt5 يعمل بشكل صحيح")

        # اختبار استيراد Django
        import django
        print("   ✅ Django يعمل بشكل صحيح")

        return True
    except ImportError as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print("🚀 بدء عملية الإصلاح الشاملة...\n")
    
    # فحص Python
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        input("\nاضغط Enter للخروج...")
        return
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # تحديث pip
    print("\n🔄 تحديث pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      capture_output=True, check=True)
        print("✅ تم تحديث pip")
    except:
        print("⚠️ تعذر تحديث pip")
    
    # فحص وتثبيت المتطلبات
    if not check_and_install_requirements():
        print("\n❌ فشل في تثبيت بعض المتطلبات")
        input("\nاضغط Enter للخروج...")
        return

    # إصلاح مشكلة Celery
    fix_celery_issue()
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("\n⚠️ تعذر إعداد قاعدة البيانات تلقائياً")
        print("💡 يرجى تشغيل الأوامر التالية يدوياً:")
        print("   python manage.py makemigrations")
        print("   python manage.py migrate")
    
    # إنشاء المستخدم الإداري
    create_superuser()
    
    # اختبار التطبيق
    if test_desktop_app():
        print("\n" + "="*60)
        print("🎉 تم إصلاح النظام بنجاح!")
        print("="*60)
        print("💡 يمكنك الآن تشغيل النظام:")
        print("   python desktop_app.py")
        print("   أو")
        print("   python start_desktop.py")
        print("\n📝 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
    else:
        print("\n⚠️ تم الإصلاح جزئياً - قد تحتاج لتثبيت PyQt5 يدوياً")
        print("💡 لتثبيت PyQt5:")
        print("   pip install PyQt5")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")
    finally:
        input("\nاضغط Enter للخروج...")
