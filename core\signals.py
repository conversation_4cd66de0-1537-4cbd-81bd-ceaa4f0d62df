from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from .activity_models import Activity, SystemLog
from .financial_models import FinancialTransaction, BankAccount
from employees.salary_models import SalarySheet, Allowance, Deduction

User = get_user_model()

@receiver(post_save, sender=User)
def log_user_creation(sender, instance, created, **kwargs):
    """تسجيل إنشاء أو تعديل المستخدمين"""
    if created:
        SystemLog.info(
            _('تم إنشاء مستخدم جديد: {username}').format(
                username=instance.username
            )
        )
    else:
        SystemLog.info(
            _('تم تعديل بيانات المستخدم: {username}').format(
                username=instance.username
            )
        )

@receiver(post_delete, sender=User)
def log_user_deletion(sender, instance, **kwargs):
    """تسجيل حذف المستخدمين"""
    SystemLog.warning(
        _('تم حذف المستخدم: {username}').format(
            username=instance.username
        )
    )

@receiver(pre_save, sender=FinancialTransaction)
def update_bank_balance(sender, instance, **kwargs):
    """تحديث رصيد الحساب البنكي عند إنشاء معاملة مالية جديدة"""
    try:
        if instance.pk:  # إذا كان تحديث لمعاملة موجودة
            old_instance = FinancialTransaction.objects.get(pk=instance.pk)
            # إلغاء تأثير المعاملة القديمة
            if old_instance.transaction_type == 'payment':
                old_instance.bank_account.current_balance += old_instance.amount
            else:
                old_instance.bank_account.current_balance -= old_instance.amount
            old_instance.bank_account.save()
        
        # تطبيق تأثير المعاملة الجديدة
        if instance.transaction_type == 'payment':
            instance.bank_account.current_balance -= instance.amount
        else:
            instance.bank_account.current_balance += instance.amount
        instance.bank_account.save()
        
    except Exception as e:
        SystemLog.error(
            _('خطأ في تحديث رصيد الحساب البنكي'),
            str(e)
        )
        raise

@receiver(post_save, sender=SalarySheet)
def log_salary_creation(sender, instance, created, **kwargs):
    """تسجيل إنشاء أو تعديل كشف راتب"""
    if created:
        Activity.log_activity(
            user=instance.created_by if hasattr(instance, 'created_by') else None,
            action=_('إنشاء كشف راتب'),
            details=_(
                'تم إنشاء كشف راتب للموظف {employee} لشهر {month}/{year}'
            ).format(
                employee=instance.employee.full_name,
                month=instance.month,
                year=instance.year
            )
        )
    else:
        Activity.log_activity(
            user=instance.modified_by if hasattr(instance, 'modified_by') else None,
            action=_('تعديل كشف راتب'),
            details=_(
                'تم تعديل كشف راتب للموظف {employee} لشهر {month}/{year}'
            ).format(
                employee=instance.employee.full_name,
                month=instance.month,
                year=instance.year
            )
        )

@receiver(post_save, sender=BankAccount)
def log_bank_account_changes(sender, instance, created, **kwargs):
    """تسجيل التغييرات على الحسابات البنكية"""
    if created:
        SystemLog.info(
            _('تم إنشاء حساب بنكي جديد: {account} - {bank}').format(
                account=instance.account_name,
                bank=instance.bank.bank_name
            )
        )
    else:
        SystemLog.info(
            _('تم تحديث بيانات الحساب البنكي: {account}').format(
                account=instance.account_name
            )
        )

@receiver([post_save, post_delete], sender=Allowance)
@receiver([post_save, post_delete], sender=Deduction)
def update_salary_totals(sender, instance, **kwargs):
    """تحديث إجماليات الراتب عند تغيير المخصصات أو الاستقطاعات"""
    try:
        # تحديث كشف الراتب
        salary_sheet = instance.employee.salarysheet_set.filter(
            month=instance.created_at.month,
            year=instance.created_at.year
        ).first()
        
        if salary_sheet:
            if sender == Allowance:
                salary_sheet.total_allowances = instance.calculate_total()
            else:
                salary_sheet.total_deductions = instance.calculate_total()
            
            salary_sheet.save()
            
    except Exception as e:
        SystemLog.error(
            _('خطأ في تحديث إجماليات الراتب'),
            str(e)
        )