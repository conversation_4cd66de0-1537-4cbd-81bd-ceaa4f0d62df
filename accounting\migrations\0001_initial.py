# Generated by Django 4.2.7 on 2025-06-05 17:37

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AccountType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="اسم نوع الحساب"
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("assets", "الأصول"),
                            ("liabilities", "الخصوم"),
                            ("equity", "حقوق الملكية"),
                            ("revenue", "الإيرادات"),
                            ("expenses", "المصروفات"),
                        ],
                        max_length=20,
                        verbose_name="فئة الحساب",
                    ),
                ),
                (
                    "code",
                    models.Char<PERSON><PERSON>(
                        max_length=10, unique=True, verbose_name="رمز النوع"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "نوع الحساب",
                "verbose_name_plural": "أنواع الحسابات",
                "db_table": "account_types",
            },
        ),
        migrations.CreateModel(
            name="Bank",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="اسم المصرف"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز المصرف"
                    ),
                ),
                (
                    "address",
                    models.TextField(blank=True, null=True, verbose_name="العنوان"),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="رقم الهاتف"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        max_length=254,
                        null=True,
                        verbose_name="البريد الإلكتروني",
                    ),
                ),
                (
                    "swift_code",
                    models.CharField(
                        blank=True, max_length=11, null=True, verbose_name="رمز SWIFT"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "المصرف",
                "verbose_name_plural": "المصارف",
                "db_table": "banks",
            },
        ),
        migrations.CreateModel(
            name="Company",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم المؤسسة")),
                ("address", models.TextField(verbose_name="العنوان")),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        max_length=254,
                        null=True,
                        verbose_name="البريد الإلكتروني",
                    ),
                ),
                (
                    "website",
                    models.URLField(
                        blank=True, null=True, verbose_name="الموقع الإلكتروني"
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="رقم الهاتف"
                    ),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="company_logos/",
                        verbose_name="شعار المؤسسة",
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "المؤسسة",
                "verbose_name_plural": "المؤسسات",
                "db_table": "companies",
            },
        ),
        migrations.CreateModel(
            name="CostCenter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="اسم مركز التكلفة"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز مركز التكلفة"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "مركز التكلفة",
                "verbose_name_plural": "مراكز التكلفة",
                "db_table": "cost_centers",
            },
        ),
        migrations.CreateModel(
            name="Currency",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم العملة")),
                ("symbol", models.CharField(max_length=10, verbose_name="رمز العملة")),
                (
                    "code",
                    models.CharField(
                        max_length=3, unique=True, verbose_name="كود العملة"
                    ),
                ),
                (
                    "subunit",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        verbose_name="أجزاء العملة",
                    ),
                ),
                (
                    "currency_type",
                    models.CharField(
                        choices=[("local", "محلية"), ("foreign", "أجنبية")],
                        max_length=10,
                        verbose_name="نوع العملة",
                    ),
                ),
                (
                    "is_default",
                    models.BooleanField(
                        default=False, verbose_name="العملة الافتراضية"
                    ),
                ),
                (
                    "exchange_rate",
                    models.DecimalField(
                        decimal_places=4,
                        default=1.0,
                        max_digits=10,
                        verbose_name="سعر الصرف",
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "العملة",
                "verbose_name_plural": "العملات",
                "db_table": "currencies",
            },
        ),
        migrations.CreateModel(
            name="FiscalPeriod",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الفترة")),
                ("fiscal_year", models.IntegerField(verbose_name="السنة المالية")),
                (
                    "start_month",
                    models.IntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="من شهر",
                    ),
                ),
                (
                    "end_month",
                    models.IntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="إلى شهر",
                    ),
                ),
                (
                    "number_of_months",
                    models.IntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="عدد الأشهر",
                    ),
                ),
                (
                    "is_current",
                    models.BooleanField(default=False, verbose_name="الفترة الحالية"),
                ),
                ("is_closed", models.BooleanField(default=False, verbose_name="مقفلة")),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "الفترة المحاسبية",
                "verbose_name_plural": "الفترات المحاسبية",
                "db_table": "fiscal_periods",
                "unique_together": {("fiscal_year", "start_month", "end_month")},
            },
        ),
        migrations.CreateModel(
            name="Department",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم الدائرة")),
                (
                    "address",
                    models.TextField(blank=True, null=True, verbose_name="العنوان"),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="رقم الهاتف"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        max_length=254,
                        null=True,
                        verbose_name="البريد الإلكتروني",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounting.company",
                        verbose_name="المؤسسة",
                    ),
                ),
            ],
            options={
                "verbose_name": "الدائرة",
                "verbose_name_plural": "الدوائر",
                "db_table": "accounting_departments",
            },
        ),
        migrations.CreateModel(
            name="CashBox",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="اسم الصندوق"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز الصندوق"
                    ),
                ),
                (
                    "opening_balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        verbose_name="الرصيد الافتتاحي",
                    ),
                ),
                (
                    "current_balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        verbose_name="الرصيد الحالي",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "currency",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="accounting.currency",
                        verbose_name="العملة",
                    ),
                ),
            ],
            options={
                "verbose_name": "الصندوق",
                "verbose_name_plural": "الصناديق",
                "db_table": "cash_boxes",
            },
        ),
        migrations.CreateModel(
            name="BankBranch",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الفرع")),
                ("code", models.CharField(max_length=10, verbose_name="رمز الفرع")),
                (
                    "address",
                    models.TextField(blank=True, null=True, verbose_name="العنوان"),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="رقم الهاتف"
                    ),
                ),
                (
                    "manager",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="مدير الفرع"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "bank",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounting.bank",
                        verbose_name="المصرف",
                    ),
                ),
            ],
            options={
                "verbose_name": "فرع المصرف",
                "verbose_name_plural": "فروع المصارف",
                "db_table": "bank_branches",
                "unique_together": {("bank", "code")},
            },
        ),
        migrations.CreateModel(
            name="BankAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "account_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الحساب"
                    ),
                ),
                (
                    "account_name",
                    models.CharField(max_length=200, verbose_name="اسم الحساب"),
                ),
                (
                    "account_type",
                    models.CharField(
                        choices=[
                            ("operational", "التشغيلية"),
                            ("payroll_treasury", "الخزينة الموحد (الرواتب)"),
                            ("savings", "توفير"),
                            ("current", "جاري"),
                            ("fixed_deposit", "وديعة ثابتة"),
                        ],
                        max_length=20,
                        verbose_name="نوع الحساب",
                    ),
                ),
                (
                    "iban",
                    models.CharField(
                        blank=True, max_length=34, null=True, verbose_name="رقم IBAN"
                    ),
                ),
                (
                    "opening_balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        verbose_name="الرصيد الافتتاحي",
                    ),
                ),
                (
                    "current_balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        verbose_name="الرصيد الحالي",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "is_default_payroll",
                    models.BooleanField(
                        default=False, verbose_name="حساب الرواتب الافتراضي"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "bank",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="accounting.bank",
                        verbose_name="المصرف",
                    ),
                ),
                (
                    "branch",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="accounting.bankbranch",
                        verbose_name="الفرع",
                    ),
                ),
                (
                    "currency",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="accounting.currency",
                        verbose_name="العملة",
                    ),
                ),
            ],
            options={
                "verbose_name": "الحساب البنكي",
                "verbose_name_plural": "الحسابات البنكية",
                "db_table": "bank_accounts",
            },
        ),
        migrations.CreateModel(
            name="Account",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="رمز الحساب"
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم الحساب")),
                (
                    "level",
                    models.CharField(
                        choices=[("main", "رئيسي"), ("sub", "فرعي")],
                        max_length=10,
                        verbose_name="مستوى الحساب",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "opening_balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        verbose_name="الرصيد الافتتاحي",
                    ),
                ),
                (
                    "current_balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=15,
                        verbose_name="الرصيد الحالي",
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "account_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="accounting.accounttype",
                        verbose_name="نوع الحساب",
                    ),
                ),
                (
                    "currency",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="accounting.currency",
                        verbose_name="العملة",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounting.account",
                        verbose_name="الحساب الأب",
                    ),
                ),
            ],
            options={
                "verbose_name": "الحساب",
                "verbose_name_plural": "الحسابات",
                "db_table": "accounts",
                "ordering": ["code"],
            },
        ),
    ]
