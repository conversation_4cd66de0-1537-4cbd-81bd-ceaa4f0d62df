from django.views.generic import ListView, <PERSON>reateView, UpdateView, DeleteView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404
from django.db.models import Sum, Q
from .models import (
    AccountGuide, Bank, BankBranch, BankAccount, FinancialTransaction,
    Currency, Organization
)
from .financial_models import Fund
from .mixins import ActivityTrackingMixin
from .forms import TransactionForm

class AccountGuideListView(LoginRequiredMixin, ListView):
    model = AccountGuide
    template_name = 'core/accounts/account_list.html'
    context_object_name = 'accounts'
    
    def get_queryset(self):
        return AccountGuide.objects.order_by('account_number')

class AccountGuideCreateView(LoginRequiredMixin, ActivityTrackingMixin, CreateView):
    model = AccountGuide
    template_name = 'core/accounts/account_form.html'
    fields = ['account_number', 'account_name', 'parent', 'notes']
    success_url = reverse_lazy('core:account_list')
    
    def form_valid(self, form):
        response = super().form_valid(form)
        self.create_activity('إنشاء حساب', f'تم إنشاء الحساب {form.instance.account_name}')
        messages.success(self.request, 'تم إنشاء الحساب بنجاح')
        return response

class AccountGuideUpdateView(LoginRequiredMixin, ActivityTrackingMixin, UpdateView):
    model = AccountGuide
    template_name = 'core/accounts/account_form.html'
    fields = ['account_number', 'account_name', 'parent', 'notes']
    success_url = reverse_lazy('core:account_list')
    
    def form_valid(self, form):
        response = super().form_valid(form)
        self.create_activity('تعديل حساب', f'تم تعديل الحساب {form.instance.account_name}')
        messages.success(self.request, 'تم تعديل الحساب بنجاح')
        return response

class AccountGuideDeleteView(LoginRequiredMixin, ActivityTrackingMixin, DeleteView):
    model = AccountGuide
    template_name = 'core/accounts/account_confirm_delete.html'
    success_url = reverse_lazy('core:account_list')
    
    def delete(self, request, *args, **kwargs):
        account = self.get_object()
        self.create_activity('حذف حساب', f'تم حذف الحساب {account.account_name}')
        messages.success(request, 'تم حذف الحساب بنجاح')
        return super().delete(request, *args, **kwargs)

class TransactionCreateMixin(LoginRequiredMixin, ActivityTrackingMixin):
    model = FinancialTransaction
    template_name = 'core/transactions/transaction_form.html'
    form_class = TransactionForm
    success_url = reverse_lazy('core:transaction_list')
    
    def get_initial(self):
        return {'transaction_type': self.transaction_type}
    
    def form_valid(self, form):
        response = super().form_valid(form)
        transaction_type = dict(FinancialTransaction.TransactionType.choices)[self.transaction_type]
        self.create_activity(
            f'إنشاء {transaction_type}',
            f'تم إنشاء {transaction_type} برقم {form.instance.transaction_number}'
        )
        messages.success(self.request, f'تم إنشاء {transaction_type} بنجاح')
        return response

class PaymentTransactionCreateView(TransactionCreateMixin, CreateView):
    transaction_type = FinancialTransaction.TransactionType.PAYMENT

class ReceiptTransactionCreateView(TransactionCreateMixin, CreateView):
    transaction_type = FinancialTransaction.TransactionType.RECEIPT

class JournalTransactionCreateView(TransactionCreateMixin, CreateView):
    transaction_type = FinancialTransaction.TransactionType.JOURNAL

class FinancialTransactionListView(LoginRequiredMixin, ListView):
    model = FinancialTransaction
    template_name = 'core/transactions/transaction_list.html'
    context_object_name = 'transactions'
    
    def get_queryset(self):
        queryset = FinancialTransaction.objects.select_related(
            'bank_account', 'account'
        ).order_by('-transaction_date', '-created_at')
        
        # Filter by date range if provided
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(
                transaction_date__range=[start_date, end_date]
            )
        
        # Filter by transaction type if provided
        transaction_type = self.request.GET.get('type')
        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['transaction_types'] = FinancialTransaction.TransactionType.choices
        return context

class DailyJournalReportView(LoginRequiredMixin, ListView):
    model = FinancialTransaction
    template_name = 'core/reports/daily_journal.html'
    context_object_name = 'transactions'
    
    def get_queryset(self):
        date = self.request.GET.get('date')
        bank_account = self.request.GET.get('bank_account')
        
        queryset = FinancialTransaction.objects.select_related(
            'bank_account', 'account'
        )
        
        if date:
            queryset = queryset.filter(transaction_date=date)
        if bank_account:
            queryset = queryset.filter(bank_account_id=bank_account)
        
        return queryset.order_by('transaction_date', 'created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['bank_accounts'] = BankAccount.objects.all()
        return context

class AccountStatementReportView(LoginRequiredMixin, ListView):
    model = FinancialTransaction
    template_name = 'core/reports/account_statement.html'
    context_object_name = 'transactions'
    
    def get_queryset(self):
        account_id = self.request.GET.get('account')
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        
        if not all([account_id, start_date, end_date]):
            return FinancialTransaction.objects.none()
        
        return FinancialTransaction.objects.filter(
            Q(account_id=account_id) |
            Q(bank_account__account_id=account_id),
            transaction_date__range=[start_date, end_date]
        ).order_by('transaction_date')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['accounts'] = AccountGuide.objects.all()
        
        account_id = self.request.GET.get('account')
        if account_id:
            context['selected_account'] = get_object_or_404(
                AccountGuide, id=account_id
            )
        
        return context

class AccountBalancesReportView(LoginRequiredMixin, TemplateView):
    template_name = 'core/reports/account_balances.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        accounts = AccountGuide.objects.all()
        balances = []
        
        for account in accounts:
            transactions = FinancialTransaction.objects.filter(
                Q(account=account) |
                Q(bank_account__account=account)
            )
            
            total_credits = transactions.filter(
                transaction_type__in=['receipt', 'journal']
            ).aggregate(Sum('amount'))['amount__sum'] or 0
            
            total_debits = transactions.filter(
                transaction_type='payment'
            ).aggregate(Sum('amount'))['amount__sum'] or 0
            
            balance = total_credits - total_debits
            
            balances.append({
                'account': account,
                'credits': total_credits,
                'debits': total_debits,
                'balance': balance
            })
        
        context['balances'] = balances
        return context