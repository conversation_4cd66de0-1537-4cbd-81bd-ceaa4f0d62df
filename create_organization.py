#!/usr/bin/env python
"""
إنشاء بيانات المؤسسة
"""

import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'simple_settings')
django.setup()

from core.models import Organization

def create_organization():
    """إنشاء بيانات المؤسسة"""
    
    print("🔄 إنشاء بيانات المؤسسة...")
    
    # إنشاء أو تحديث المؤسسة
    org, created = Organization.objects.get_or_create(
        pk=1,
        defaults={
            'name': 'شركة النظام المتطور للرواتب والمحاسبة',
            'email': '<EMAIL>',
            'website': 'https://www.payroll-system.com'
        }
    )
    
    if created:
        print(f"✅ تم إنشاء المؤسسة: {org.name}")
    else:
        print(f"✅ المؤسسة موجودة: {org.name}")
    
    print("\n🎉 تم إعداد بيانات المؤسسة بنجاح!")

if __name__ == '__main__':
    create_organization()
