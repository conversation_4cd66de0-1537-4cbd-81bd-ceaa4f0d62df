# Generated by Django 4.2.7 on 2025-06-05 17:37

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Dashboard",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=200, verbose_name="اسم لوحة المعلومات"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                ("layout", models.JSONField(default=dict, verbose_name="تخطيط اللوحة")),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="افتراضية"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشطة")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "لوحة المعلومات",
                "verbose_name_plural": "لوحات المعلومات",
                "db_table": "dashboards",
            },
        ),
        migrations.CreateModel(
            name="DashboardWidget",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم العنصر")),
                (
                    "widget_type",
                    models.CharField(
                        choices=[
                            ("chart", "رسم بياني"),
                            ("table", "جدول"),
                            ("kpi", "مؤشر أداء"),
                            ("text", "نص"),
                        ],
                        max_length=20,
                        verbose_name="نوع العنصر",
                    ),
                ),
                ("data_source", models.TextField(verbose_name="مصدر البيانات")),
                (
                    "configuration",
                    models.JSONField(default=dict, verbose_name="التكوين"),
                ),
                (
                    "position_x",
                    models.IntegerField(default=0, verbose_name="الموضع الأفقي"),
                ),
                (
                    "position_y",
                    models.IntegerField(default=0, verbose_name="الموضع العمودي"),
                ),
                ("width", models.IntegerField(default=4, verbose_name="العرض")),
                ("height", models.IntegerField(default=3, verbose_name="الارتفاع")),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "عنصر لوحة المعلومات",
                "verbose_name_plural": "عناصر لوحات المعلومات",
                "db_table": "dashboard_widgets",
            },
        ),
        migrations.CreateModel(
            name="ReportExecution",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "parameters_used",
                    models.JSONField(default=dict, verbose_name="المعاملات المستخدمة"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "في الانتظار"),
                            ("running", "قيد التنفيذ"),
                            ("completed", "مكتمل"),
                            ("failed", "فشل"),
                        ],
                        default="pending",
                        max_length=15,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "result_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="reports/",
                        verbose_name="ملف النتيجة",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, null=True, verbose_name="رسالة الخطأ"),
                ),
                (
                    "execution_time",
                    models.DurationField(
                        blank=True, null=True, verbose_name="وقت التنفيذ"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ التنفيذ"
                    ),
                ),
            ],
            options={
                "verbose_name": "تنفيذ التقرير",
                "verbose_name_plural": "تنفيذات التقارير",
                "db_table": "report_executions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ReportTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم التقرير")),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("payroll", "تقارير الرواتب"),
                            ("financial", "تقارير مالية"),
                            ("employee", "تقارير الموظفين"),
                            ("custom", "تقارير مخصصة"),
                        ],
                        max_length=20,
                        verbose_name="نوع التقرير",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "sql_query",
                    models.TextField(blank=True, null=True, verbose_name="استعلام SQL"),
                ),
                (
                    "parameters",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="المعاملات"
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "قالب التقرير",
                "verbose_name_plural": "قوالب التقارير",
                "db_table": "report_templates",
            },
        ),
    ]
