from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'users'

urlpatterns = [
    # تسجيل الدخول والخروج
    path('login/', auth_views.LoginView.as_view(template_name='users/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    
    # لوحة المعلومات
    path('', views.DashboardView.as_view(), name='dashboard'),
    
    # إدارة المستخدمين
    path('users/', views.UserListView.as_view(), name='user_list'),
    path('users/create/', views.UserCreateView.as_view(), name='user_create'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    path('users/<int:pk>/edit/', views.UserUpdateView.as_view(), name='user_edit'),
    path('users/<int:pk>/delete/', views.UserDeleteView.as_view(), name='user_delete'),
    
    # مجموعات المستخدمين
    path('groups/', views.UserGroupListView.as_view(), name='group_list'),
    path('groups/create/', views.UserGroupCreateView.as_view(), name='group_create'),
    path('groups/<int:pk>/edit/', views.UserGroupUpdateView.as_view(), name='group_edit'),
    path('groups/<int:pk>/delete/', views.UserGroupDeleteView.as_view(), name='group_delete'),
    
    # سجلات التدقيق
    path('audit-logs/', views.AuditLogListView.as_view(), name='audit_log_list'),
]
