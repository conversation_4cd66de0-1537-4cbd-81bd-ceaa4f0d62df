# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Environment Variables
.env
.env.local
.env.*.local

# Database
*.sqlite3
*.db
*.sql

# IDE
.idea/
.vscode/
*.swp
*.swo
*.swn
.DS_Store
Thumbs.db

# Project specific
backups/
cache/
logs/
temp/
uploads/

# Compressed files
*.gz
*.tar
*.zip
*.rar
*.7z

# Compiled translations
*.mo
*.pot

# Test coverage
htmlcov/
.tox/
.coverage
.coverage.*
.cache
coverage.xml
*.cover

# Documentation
docs/_build/
docs/api/

# Node (if using any JavaScript tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development settings
local.py
local_settings.py

# System files
.directory
desktop.ini

# SSL/TLS Certificates
*.pem
*.key
*.crt
*.cer
*.der
*.p12
*.pfx

# Sensitive data
credentials/
secrets/