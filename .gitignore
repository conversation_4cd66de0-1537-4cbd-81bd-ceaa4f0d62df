# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Django specific
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# PyQt5 specific
*.ui~

# IDE specific
.vscode/
.idea/
*.swp
*.swo
*~

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
backups/
uploads/
temp/
cache/

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Configuration files with sensitive data
local_settings.py
production_settings.py
.env.local
.env.production

# Compiled translations
*.mo

# Backup files
*.bak
*.backup
*.old

# Temporary files
*.tmp
*.temp

# Node.js (if using for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
htmlcov/
.coverage
coverage.xml

# pytest
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Custom additions for this project
# Database files
*.db
*.sqlite
*.sqlite3

# Log files
*.log
logs/

# Media files (uploads)
media/

# Static files (collected)
staticfiles/

# Backup files
backups/
*.backup
*.bak

# Temporary files
temp/
tmp/
cache/

# IDE and editor files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows specific
desktop.ini

# PyQt5 UI files
*.ui~

# Compiled Python files
*.pyc
*.pyo
*.pyd
__pycache__/

# Virtual environments
venv/
env/
.venv/
.env/

# Configuration files
.env
.env.local
.env.production
local_settings.py

# SSL certificates
*.pem
*.key
*.crt
ssl/

# Reports and exports
reports/exports/
*.pdf
*.xlsx
*.csv

# Test files
test_*.py
*_test.py
tests/

# Documentation build
docs/_build/
docs/build/

# Package files
*.egg-info/
dist/
build/
