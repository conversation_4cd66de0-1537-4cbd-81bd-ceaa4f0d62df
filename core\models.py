from django.db import models
# from django.core.validators import FileExtensionValidator  # معطل مؤقتاً
from django.utils.translation import gettext_lazy as _

class Organization(models.Model):
    """نموذج بيانات المؤسسة - مبسط"""
    name = models.CharField(
        _('اسم المؤسسة'),
        max_length=255,
        default='شركة النظام'
    )
    email = models.EmailField(
        _('البريد الإلكتروني'),
        blank=True
    )
    website = models.URLField(
        _('الموقع الإلكتروني'),
        blank=True
    )

    class Meta:
        verbose_name = _('المؤسسة')
        verbose_name_plural = _('المؤسسة')

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # التأكد من وجود مؤسسة واحدة فقط
        if not self.pk and Organization.objects.exists():
            return
        super().save(*args, **kwargs)

    @classmethod
    def get_solo(cls):
        """الحصول على كائن المؤسسة الوحيد"""
        obj, created = cls.objects.get_or_create(pk=1)
        return obj
