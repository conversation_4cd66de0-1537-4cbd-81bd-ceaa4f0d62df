from django.db import models
from django.core.validators import FileExtensionValidator
from django.utils.translation import gettext_lazy as _

class Organization(models.Model):
    """نموذج بيانات المؤسسة"""
    name = models.CharField(
        _('اسم المؤسسة'), 
        max_length=255
    )
    address = models.TextField(
        _('العنوان'),
        blank=True
    )
    email = models.EmailField(
        _('البريد الإلكتروني'),
        blank=True
    )
    website = models.URLField(
        _('الموقع الإلكتروني'),
        blank=True
    )
    phone = models.CharField(
        _('رقم الهاتف'),
        max_length=20,
        blank=True
    )
    logo = models.ImageField(
        _('الشعار'),
        upload_to='organization_logos/',
        validators=[FileExtensionValidator(['png', 'jpg', 'jpeg', 'svg'])],
        blank=True
    )
    notes = models.TextField(
        _('ملاحظات'),
        blank=True
    )
    sequences = models.JSONField(
        _('التسلسلات'),
        default=dict,
        help_text=_('تخزين التسلسلات المختلفة في النظام')
    )
    created_at = models.DateTimeField(
        _('تاريخ الإنشاء'),
        auto_now_add=True
    )
    updated_at = models.DateTimeField(
        _('تاريخ التحديث'),
        auto_now=True
    )

    class Meta:
        verbose_name = _('المؤسسة')
        verbose_name_plural = _('المؤسسة')

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # التأكد من وجود مؤسسة واحدة فقط
        if not self.pk and Organization.objects.exists():
            return
        super().save(*args, **kwargs)

    @classmethod
    def get_solo(cls):
        """الحصول على كائن المؤسسة الوحيد"""
        obj, created = cls.objects.get_or_create(pk=1)
        return obj
