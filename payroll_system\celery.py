import os
from celery import Celery
from django.conf import settings

# تعيين إعدادات Django الافتراضية لـ Celery
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'payroll_system.settings')

app = Celery('payroll_system')

# استخدام إعدادات Django مع بادئة CELERY
app.config_from_object('django.conf:settings', namespace='CELERY')

# البحث التلقائي عن المهام في جميع التطبيقات
app.autodiscover_tasks()

# إعدادات Celery
app.conf.update(
    # إعدادات الوسيط (Redis)
    broker_url=os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
    result_backend=os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
    
    # إعدادات المهام
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Baghdad',
    enable_utc=True,
    
    # إعدادات الأداء
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # إعدادات المهام المجدولة
    beat_schedule={
        # نسخة احتياطية يومية
        'daily-backup': {
            'task': 'payroll_system.tasks.create_daily_backup',
            'schedule': 60.0 * 60.0 * 24,  # كل 24 ساعة
        },
        # تنظيف الملفات المؤقتة
        'cleanup-temp-files': {
            'task': 'payroll_system.tasks.cleanup_temp_files',
            'schedule': 60.0 * 60.0 * 6,  # كل 6 ساعات
        },
        # تحديث أسعار الصرف
        'update-exchange-rates': {
            'task': 'accounting.tasks.update_exchange_rates',
            'schedule': 60.0 * 60.0 * 12,  # كل 12 ساعة
        },
        # إرسال تقارير دورية
        'send-periodic-reports': {
            'task': 'reports.tasks.send_periodic_reports',
            'schedule': 60.0 * 60.0 * 24 * 7,  # أسبوعياً
        },
    },
)

@app.task(bind=True)
def debug_task(self):
    """مهمة تجريبية للتأكد من عمل Celery"""
    print(f'Request: {self.request!r}')
