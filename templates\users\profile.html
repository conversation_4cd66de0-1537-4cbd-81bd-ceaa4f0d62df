{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block title %}الملف الشخصي - {{ user.get_full_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">الملف الشخصي</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.first_name|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.last_name|as_crispy_field }}
                            </div>
                        </div>
                        {{ form.email|as_crispy_field }}
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                            <a href="{% url 'users:password_change' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- User Information -->
            <div class="card shadow mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">معلومات الحساب</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم المستخدم:</strong> {{ user.username }}</p>
                            <p><strong>تاريخ الانضمام:</strong> {{ user.date_joined|date:"Y-m-d" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>آخر دخول:</strong> {{ user.last_login|date:"Y-m-d H:i" }}</p>
                            <p><strong>نوع الحساب:</strong> {{ user.get_account_type_display }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="card shadow mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">آخر النشاطات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النشاط</th>
                                    <th>التفاصيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in user.activity_set.all|slice:":5" %}
                                <tr>
                                    <td>{{ activity.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>{{ activity.action }}</td>
                                    <td>{{ activity.details|default:"-" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">لا توجد نشاطات حديثة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}