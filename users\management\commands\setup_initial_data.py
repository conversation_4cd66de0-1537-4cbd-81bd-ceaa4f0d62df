"""
أمر Django لإعداد البيانات الأولية للنظام
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction

from users.models import UserGroup
from employees.models import Department, Division, JobTitle, Certificate, JobGrade, JobStage
from accounting.models import Company, Currency, AccountType, FiscalPeriod
from payroll.models import AllowanceType, DeductionType, TaxBracket

User = get_user_model()


class Command(BaseCommand):
    help = 'إعداد البيانات الأولية للنظام المحاسبي الموحد'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='إعادة إنشاء البيانات حتى لو كانت موجودة',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 بدء إعداد البيانات الأولية للنظام المحاسبي الموحد')
        )

        try:
            with transaction.atomic():
                self.create_user_groups()
                self.create_company_data()
                self.create_currencies()
                self.create_account_types()
                self.create_fiscal_period()
                self.create_employee_data()
                self.create_payroll_data()
                
                if options['force']:
                    self.stdout.write(
                        self.style.WARNING('⚠️ تم استخدام خيار --force، تم إعادة إنشاء جميع البيانات')
                    )

            self.stdout.write(
                self.style.SUCCESS('✅ تم إعداد البيانات الأولية بنجاح!')
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ خطأ في إعداد البيانات الأولية: {str(e)}')
            )

    def create_user_groups(self):
        """إنشاء مجموعات المستخدمين الأساسية"""
        self.stdout.write('📋 إنشاء مجموعات المستخدمين...')
        
        groups = [
            {'name': 'المديرون', 'notes': 'مجموعة المديرين مع صلاحيات كاملة'},
            {'name': 'محاسبون', 'notes': 'مجموعة المحاسبين'},
            {'name': 'موظفو الرواتب', 'notes': 'مجموعة موظفي إدارة الرواتب'},
            {'name': 'موظفو شؤون الموظفين', 'notes': 'مجموعة موظفي شؤون الموظفين'},
            {'name': 'مستخدمون عاديون', 'notes': 'مجموعة المستخدمين العاديين'},
        ]
        
        for group_data in groups:
            group, created = UserGroup.objects.get_or_create(
                name=group_data['name'],
                defaults={'notes': group_data['notes']}
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء مجموعة: {group.name}')

    def create_company_data(self):
        """إنشاء بيانات الشركة الأساسية"""
        self.stdout.write('🏢 إنشاء بيانات الشركة...')
        
        company, created = Company.objects.get_or_create(
            name='الشركة النموذجية',
            defaults={
                'address': 'العراق - بغداد',
                'email': '<EMAIL>',
                'phone': '+964-xxx-xxx-xxxx',
                'notes': 'بيانات الشركة النموذجية'
            }
        )
        if created:
            self.stdout.write(f'  ✅ تم إنشاء الشركة: {company.name}')

    def create_currencies(self):
        """إنشاء العملات الأساسية"""
        self.stdout.write('💰 إنشاء العملات...')
        
        currencies = [
            {
                'name': 'الدينار العراقي',
                'symbol': 'د.ع',
                'code': 'IQD',
                'subunit': 'فلس',
                'currency_type': 'local',
                'is_default': True,
                'exchange_rate': 1.0000
            },
            {
                'name': 'الدولار الأمريكي',
                'symbol': '$',
                'code': 'USD',
                'subunit': 'سنت',
                'currency_type': 'foreign',
                'is_default': False,
                'exchange_rate': 1310.0000
            },
        ]
        
        for currency_data in currencies:
            currency, created = Currency.objects.get_or_create(
                code=currency_data['code'],
                defaults=currency_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء العملة: {currency.name}')

    def create_account_types(self):
        """إنشاء أنواع الحسابات"""
        self.stdout.write('📊 إنشاء أنواع الحسابات...')
        
        account_types = [
            {'name': 'الأصول الثابتة', 'category': 'assets', 'code': '1000'},
            {'name': 'الأصول المتداولة', 'category': 'assets', 'code': '1100'},
            {'name': 'الخصوم طويلة الأجل', 'category': 'liabilities', 'code': '2000'},
            {'name': 'الخصوم قصيرة الأجل', 'category': 'liabilities', 'code': '2100'},
            {'name': 'رأس المال', 'category': 'equity', 'code': '3000'},
            {'name': 'الإيرادات التشغيلية', 'category': 'revenue', 'code': '4000'},
            {'name': 'المصروفات التشغيلية', 'category': 'expenses', 'code': '5000'},
            {'name': 'مصروفات الرواتب', 'category': 'expenses', 'code': '5100'},
        ]
        
        for account_type_data in account_types:
            account_type, created = AccountType.objects.get_or_create(
                code=account_type_data['code'],
                defaults=account_type_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء نوع الحساب: {account_type.name}')

    def create_fiscal_period(self):
        """إنشاء الفترة المحاسبية الحالية"""
        self.stdout.write('📅 إنشاء الفترة المحاسبية...')
        
        from datetime import datetime
        current_year = datetime.now().year
        
        period, created = FiscalPeriod.objects.get_or_create(
            fiscal_year=current_year,
            defaults={
                'name': f'السنة المالية {current_year}',
                'start_month': 1,
                'end_month': 12,
                'number_of_months': 12,
                'is_current': True,
                'notes': f'الفترة المحاسبية للسنة {current_year}'
            }
        )
        if created:
            self.stdout.write(f'  ✅ تم إنشاء الفترة المحاسبية: {period.name}')

    def create_employee_data(self):
        """إنشاء البيانات الأساسية للموظفين"""
        self.stdout.write('👥 إنشاء البيانات الأساسية للموظفين...')
        
        # الأقسام
        departments = [
            {'name': 'الإدارة العامة', 'code': 'ADM'},
            {'name': 'المحاسبة والمالية', 'code': 'ACC'},
            {'name': 'شؤون الموظفين', 'code': 'HR'},
            {'name': 'تقنية المعلومات', 'code': 'IT'},
        ]
        
        for dept_data in departments:
            dept, created = Department.objects.get_or_create(
                code=dept_data['code'],
                defaults=dept_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء القسم: {dept.name}')

        # العناوين الوظيفية
        job_titles = [
            {'name': 'مدير عام', 'code': 'GM'},
            {'name': 'مدير قسم', 'code': 'DM'},
            {'name': 'محاسب أول', 'code': 'SA'},
            {'name': 'محاسب', 'code': 'ACC'},
            {'name': 'موظف', 'code': 'EMP'},
        ]
        
        for title_data in job_titles:
            title, created = JobTitle.objects.get_or_create(
                code=title_data['code'],
                defaults=title_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء العنوان الوظيفي: {title.name}')

        # الشهادات
        certificates = [
            {'name': 'دكتوراه', 'code': 'PHD'},
            {'name': 'ماجستير', 'code': 'MSC'},
            {'name': 'بكالوريوس', 'code': 'BSC'},
            {'name': 'دبلوم', 'code': 'DIP'},
            {'name': 'إعدادية', 'code': 'HS'},
        ]
        
        for cert_data in certificates:
            cert, created = Certificate.objects.get_or_create(
                code=cert_data['code'],
                defaults=cert_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء الشهادة: {cert.name}')

        # الدرجات الوظيفية
        job_grades = [
            {'name': 'الدرجة الأولى', 'code': 'G1'},
            {'name': 'الدرجة الثانية', 'code': 'G2'},
            {'name': 'الدرجة الثالثة', 'code': 'G3'},
            {'name': 'الدرجة الرابعة', 'code': 'G4'},
            {'name': 'الدرجة الخامسة', 'code': 'G5'},
        ]
        
        for grade_data in job_grades:
            grade, created = JobGrade.objects.get_or_create(
                code=grade_data['code'],
                defaults=grade_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء الدرجة الوظيفية: {grade.name}')

        # المراحل
        job_stages = [
            {'name': 'المرحلة الأولى', 'code': 'S1'},
            {'name': 'المرحلة الثانية', 'code': 'S2'},
            {'name': 'المرحلة الثالثة', 'code': 'S3'},
            {'name': 'المرحلة الرابعة', 'code': 'S4'},
            {'name': 'المرحلة الخامسة', 'code': 'S5'},
        ]
        
        for stage_data in job_stages:
            stage, created = JobStage.objects.get_or_create(
                code=stage_data['code'],
                defaults=stage_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء المرحلة: {stage.name}')

    def create_payroll_data(self):
        """إنشاء البيانات الأساسية للرواتب"""
        self.stdout.write('💼 إنشاء البيانات الأساسية للرواتب...')
        
        # أنواع المخصصات
        allowance_types = [
            {'name': 'مخصصات المنصب', 'code': 'POS', 'is_percentage': False, 'default_amount': 0},
            {'name': 'مخصصات الزوجية', 'code': 'MAR', 'is_percentage': False, 'default_amount': 25000},
            {'name': 'مخصصات الأولاد', 'code': 'CHI', 'is_percentage': False, 'default_amount': 15000},
            {'name': 'مخصصات هندسية', 'code': 'ENG', 'is_percentage': False, 'default_amount': 0},
            {'name': 'مخصصات الشهادة', 'code': 'CER', 'is_percentage': False, 'default_amount': 0},
            {'name': 'مخصصات الحرفة', 'code': 'CRA', 'is_percentage': False, 'default_amount': 0},
            {'name': 'مخصصات الخطورة', 'code': 'DAN', 'is_percentage': False, 'default_amount': 0},
            {'name': 'مخصصات الموقع الجغرافي', 'code': 'LOC', 'is_percentage': False, 'default_amount': 0},
        ]
        
        for allowance_data in allowance_types:
            allowance, created = AllowanceType.objects.get_or_create(
                code=allowance_data['code'],
                defaults=allowance_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء نوع المخصص: {allowance.name}')

        # أنواع الاستقطاعات
        deduction_types = [
            {'name': 'صندوق تقاعد موظفي الدولة', 'code': 'RET', 'is_percentage': True, 'default_amount': 10, 'is_mandatory': True},
            {'name': 'حصة الدائرة في المساهمة الحكومية', 'code': 'GOV', 'is_percentage': True, 'default_amount': 15, 'is_mandatory': True},
            {'name': 'ضريبة الدخل', 'code': 'TAX', 'is_percentage': False, 'default_amount': 0, 'is_mandatory': True},
            {'name': 'صندوق هيئة الحماية الاجتماعية', 'code': 'SOC', 'is_percentage': False, 'default_amount': 0},
            {'name': 'أمانات الضمان الصحي', 'code': 'HEA', 'is_percentage': False, 'default_amount': 0},
            {'name': 'دوائر وجهات أخرى', 'code': 'OTH', 'is_percentage': False, 'default_amount': 0},
            {'name': 'دوائر التنفيذ', 'code': 'EXE', 'is_percentage': False, 'default_amount': 0},
            {'name': 'أقساط المصارف', 'code': 'BAN', 'is_percentage': False, 'default_amount': 0},
        ]
        
        for deduction_data in deduction_types:
            deduction, created = DeductionType.objects.get_or_create(
                code=deduction_data['code'],
                defaults=deduction_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء نوع الاستقطاع: {deduction.name}')

        # شرائح الضريبة
        tax_brackets = [
            {'name': 'الشريحة الأولى', 'min_amount': 0, 'max_amount': 250000, 'tax_rate': 0},
            {'name': 'الشريحة الثانية', 'min_amount': 250000, 'max_amount': 500000, 'tax_rate': 3},
            {'name': 'الشريحة الثالثة', 'min_amount': 500000, 'max_amount': 1000000, 'tax_rate': 5},
            {'name': 'الشريحة الرابعة', 'min_amount': 1000000, 'max_amount': None, 'tax_rate': 15},
        ]
        
        for bracket_data in tax_brackets:
            bracket, created = TaxBracket.objects.get_or_create(
                name=bracket_data['name'],
                defaults=bracket_data
            )
            if created:
                self.stdout.write(f'  ✅ تم إنشاء شريحة الضريبة: {bracket.name}')

        self.stdout.write(
            self.style.SUCCESS('✅ تم إنشاء جميع البيانات الأساسية بنجاح!')
        )
