#!/usr/bin/env python
"""
إنشاء بيانات تجريبية للنظام
"""

import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'simple_settings')
django.setup()

from employees.models import Department, Section, Division, JobTitle, Certificate, Degree, Stage

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    
    print("🔄 إنشاء بيانات تجريبية...")
    
    # إنشاء الدوائر
    departments = [
        {'department_number': 'D001', 'name': 'دائرة الموارد البشرية', 'phone': '07801234567', 'email': '<EMAIL>'},
        {'department_number': 'D002', 'name': 'دائرة المحاسبة', 'phone': '07801234568', 'email': '<EMAIL>'},
        {'department_number': 'D003', 'name': 'دائرة تقنية المعلومات', 'phone': '07801234569', 'email': '<EMAIL>'},
        {'department_number': 'D004', 'name': 'دائرة التسويق', 'phone': '07801234570', 'email': '<EMAIL>'},
    ]
    
    for dept_data in departments:
        dept, created = Department.objects.get_or_create(
            department_number=dept_data['department_number'],
            defaults=dept_data
        )
        if created:
            print(f"✅ تم إنشاء الدائرة: {dept.name}")
    
    # إنشاء الأقسام
    sections = [
        {'section_number': 'S001', 'name': 'قسم التوظيف'},
        {'section_number': 'S002', 'name': 'قسم التدريب'},
        {'section_number': 'S003', 'name': 'قسم الحسابات العامة'},
        {'section_number': 'S004', 'name': 'قسم المراجعة'},
        {'section_number': 'S005', 'name': 'قسم البرمجة'},
        {'section_number': 'S006', 'name': 'قسم الشبكات'},
    ]
    
    for section_data in sections:
        section, created = Section.objects.get_or_create(
            section_number=section_data['section_number'],
            defaults=section_data
        )
        if created:
            print(f"✅ تم إنشاء القسم: {section.name}")
    
    # إنشاء العناوين الوظيفية
    job_titles = [
        {'title_number': 'JT001', 'name': 'مدير عام'},
        {'title_number': 'JT002', 'name': 'مدير دائرة'},
        {'title_number': 'JT003', 'name': 'رئيس قسم'},
        {'title_number': 'JT004', 'name': 'موظف أول'},
        {'title_number': 'JT005', 'name': 'موظف'},
        {'title_number': 'JT006', 'name': 'محاسب'},
        {'title_number': 'JT007', 'name': 'مبرمج'},
        {'title_number': 'JT008', 'name': 'مصمم'},
    ]
    
    for job_data in job_titles:
        job, created = JobTitle.objects.get_or_create(
            title_number=job_data['title_number'],
            defaults=job_data
        )
        if created:
            print(f"✅ تم إنشاء العنوان الوظيفي: {job.name}")
    
    # إنشاء الشهادات
    certificates = [
        {'certificate_number': 'C001', 'name': 'بكالوريوس'},
        {'certificate_number': 'C002', 'name': 'ماجستير'},
        {'certificate_number': 'C003', 'name': 'دكتوراه'},
        {'certificate_number': 'C004', 'name': 'دبلوم'},
        {'certificate_number': 'C005', 'name': 'إعدادية'},
    ]
    
    for cert_data in certificates:
        cert, created = Certificate.objects.get_or_create(
            certificate_number=cert_data['certificate_number'],
            defaults=cert_data
        )
        if created:
            print(f"✅ تم إنشاء الشهادة: {cert.name}")
    
    # إنشاء الدرجات الوظيفية
    degrees = [
        {'degree_number': 'DG001', 'name': 'الدرجة الأولى'},
        {'degree_number': 'DG002', 'name': 'الدرجة الثانية'},
        {'degree_number': 'DG003', 'name': 'الدرجة الثالثة'},
        {'degree_number': 'DG004', 'name': 'الدرجة الرابعة'},
        {'degree_number': 'DG005', 'name': 'الدرجة الخامسة'},
    ]
    
    for degree_data in degrees:
        degree, created = Degree.objects.get_or_create(
            degree_number=degree_data['degree_number'],
            defaults=degree_data
        )
        if created:
            print(f"✅ تم إنشاء الدرجة: {degree.name}")
    
    # إنشاء المراحل
    stages = [
        {'stage_number': 'ST001', 'name': 'المرحلة الأولى'},
        {'stage_number': 'ST002', 'name': 'المرحلة الثانية'},
        {'stage_number': 'ST003', 'name': 'المرحلة الثالثة'},
        {'stage_number': 'ST004', 'name': 'المرحلة الرابعة'},
        {'stage_number': 'ST005', 'name': 'المرحلة الخامسة'},
    ]
    
    for stage_data in stages:
        stage, created = Stage.objects.get_or_create(
            stage_number=stage_data['stage_number'],
            defaults=stage_data
        )
        if created:
            print(f"✅ تم إنشاء المرحلة: {stage.name}")
    
    print("\n🎉 تم إنشاء جميع البيانات التجريبية بنجاح!")
    print("\n📋 ملخص البيانات المُنشأة:")
    print(f"   📁 الدوائر: {Department.objects.count()}")
    print(f"   📂 الأقسام: {Section.objects.count()}")
    print(f"   💼 العناوين الوظيفية: {JobTitle.objects.count()}")
    print(f"   🎓 الشهادات: {Certificate.objects.count()}")
    print(f"   📊 الدرجات: {Degree.objects.count()}")
    print(f"   🔢 المراحل: {Stage.objects.count()}")

if __name__ == '__main__':
    create_sample_data()
