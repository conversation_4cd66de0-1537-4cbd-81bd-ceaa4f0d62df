from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    # Dashboard
    path('', views.DashboardView.as_view(), name='dashboard'),

    # Organization URLs
    path('organization/setup/',
         views.OrganizationSetupView.as_view(),
         name='organization_setup'),
    
    # Database management URLs
    path('database/backup/',
         views.BackupDatabaseView.as_view(),
         name='backup_database'),
    
    path('database/restore/',
         views.RestoreDatabaseView.as_view(),
         name='restore_database'),
    
    # Financial URLs - will be implemented later
    path('accounts/',
         views.AccountGuideListView.as_view(),
         name='account_list'),
    
    path('accounts/add/',
         views.AccountGuideCreateView.as_view(),
         name='account_create'),
    
    path('accounts/<int:pk>/edit/',
         views.AccountGuideUpdateView.as_view(),
         name='account_update'),
    
    path('accounts/<int:pk>/delete/',
         views.AccountGuideDeleteView.as_view(),
         name='account_delete'),
    
    # Bank URLs
    path('banks/',
         views.BankListView.as_view(),
         name='bank_list'),
    
    path('banks/add/',
         views.BankCreateView.as_view(),
         name='bank_create'),
    
    path('banks/<int:pk>/edit/',
         views.BankUpdateView.as_view(),
         name='bank_update'),
    
    path('banks/<int:pk>/delete/',
         views.BankDeleteView.as_view(),
         name='bank_delete'),
    
    # Bank Branch URLs
    path('banks/<int:bank_id>/branches/',
         views.BankBranchListView.as_view(),
         name='branch_list'),
    
    path('banks/<int:bank_id>/branches/add/',
         views.BankBranchCreateView.as_view(),
         name='branch_create'),
    
    path('banks/branches/<int:pk>/edit/',
         views.BankBranchUpdateView.as_view(),
         name='branch_update'),
    
    path('banks/branches/<int:pk>/delete/',
         views.BankBranchDeleteView.as_view(),
         name='branch_delete'),
    
    # Bank Account URLs
    path('bank-accounts/',
         views.BankAccountListView.as_view(),
         name='bank_account_list'),
    
    path('bank-accounts/add/',
         views.BankAccountCreateView.as_view(),
         name='bank_account_create'),
    
    path('bank-accounts/<int:pk>/edit/',
         views.BankAccountUpdateView.as_view(),
         name='bank_account_update'),
    
    path('bank-accounts/<int:pk>/delete/',
         views.BankAccountDeleteView.as_view(),
         name='bank_account_delete'),
    
    # Transaction URLs
    path('transactions/',
         views.FinancialTransactionListView.as_view(),
         name='transaction_list'),
    
    path('transactions/payment/add/',
         views.PaymentTransactionCreateView.as_view(),
         name='payment_create'),
    
    path('transactions/receipt/add/',
         views.ReceiptTransactionCreateView.as_view(),
         name='receipt_create'),
    
    path('transactions/journal/add/',
         views.JournalTransactionCreateView.as_view(),
         name='journal_create'),
    
    path('transactions/<int:pk>/edit/',
         views.FinancialTransactionUpdateView.as_view(),
         name='transaction_update'),
    
    path('transactions/<int:pk>/delete/',
         views.FinancialTransactionDeleteView.as_view(),
         name='transaction_delete'),
    
    # Report URLs
    path('reports/daily-journal/',
         views.DailyJournalReportView.as_view(),
         name='daily_journal_report'),
    
    path('reports/account-statement/',
         views.AccountStatementReportView.as_view(),
         name='account_statement_report'),
    
    path('reports/account-balances/',
         views.AccountBalancesReportView.as_view(),
         name='account_balances_report'),
    
    path('reports/opening-balances/',
         views.OpeningBalancesReportView.as_view(),
         name='opening_balances_report'),
]