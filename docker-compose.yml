version: '3.8'

services:
  # قاعدة البيانات PostgreSQL
  db:
    image: postgres:15
    container_name: payroll_db
    environment:
      POSTGRES_DB: payroll_system
      POSTGRES_USER: payroll_user
      POSTGRES_PASSWORD: payroll_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis للمهام الخلفية
  redis:
    image: redis:7-alpine
    container_name: payroll_redis
    ports:
      - "6379:6379"
    restart: unless-stopped

  # تطبيق Django
  web:
    build: .
    container_name: payroll_web
    command: gunicorn --bind 0.0.0.0:8000 payroll_system.wsgi:application
    volumes:
      - .:/app
      - static_volume:/app/static
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DB_ENGINE=django.db.backends.postgresql
      - DB_NAME=payroll_system
      - DB_USER=payroll_user
      - DB_PASSWORD=payroll_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Celery Worker للمهام الخلفية
  celery:
    build: .
    container_name: payroll_celery
    command: celery -A payroll_system worker --loglevel=info
    volumes:
      - .:/app
    environment:
      - DEBUG=False
      - DB_ENGINE=django.db.backends.postgresql
      - DB_NAME=payroll_system
      - DB_USER=payroll_user
      - DB_PASSWORD=payroll_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Celery Beat للمهام المجدولة
  celery-beat:
    build: .
    container_name: payroll_celery_beat
    command: celery -A payroll_system beat --loglevel=info
    volumes:
      - .:/app
    environment:
      - DEBUG=False
      - DB_ENGINE=django.db.backends.postgresql
      - DB_NAME=payroll_system
      - DB_USER=payroll_user
      - DB_PASSWORD=payroll_password
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Nginx للملفات الثابتة
  nginx:
    image: nginx:alpine
    container_name: payroll_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/static
      - media_volume:/app/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped

volumes:
  postgres_data:
  static_volume:
  media_volume:
