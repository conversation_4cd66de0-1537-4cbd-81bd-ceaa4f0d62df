from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (Department, Division, JobTitle, Certificate,
                    JobGrade, JobStage, Employee)


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'created_at']
    search_fields = ['name', 'code']
    list_filter = ['created_at']
    ordering = ['name']


@admin.register(Division)
class DivisionAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'department', 'created_at']
    search_fields = ['name', 'code', 'department__name']
    list_filter = ['department', 'created_at']
    ordering = ['department__name', 'name']


@admin.register(JobTitle)
class JobTitleAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'created_at']
    search_fields = ['name', 'code']
    list_filter = ['created_at']
    ordering = ['name']


@admin.register(Certificate)
class CertificateAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'created_at']
    search_fields = ['name', 'code']
    list_filter = ['created_at']
    ordering = ['name']


@admin.register(JobGrade)
class JobGradeAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'created_at']
    search_fields = ['name', 'code']
    list_filter = ['created_at']
    ordering = ['name']


@admin.register(JobStage)
class JobStageAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'created_at']
    search_fields = ['name', 'code']
    list_filter = ['created_at']
    ordering = ['name']


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['employee_number', 'full_name', 'department', 'job_title',
                   'status', 'hire_date']
    search_fields = ['employee_number', 'full_name', 'iban_number']
    list_filter = ['status', 'gender', 'marital_status', 'department',
                  'job_title', 'hire_date']
    ordering = ['employee_number']

    fieldsets = (
        (_('البيانات الأساسية'), {
            'fields': ('employee_number', 'iban_number', 'full_name', 'gender',
                      'marital_status', 'birth_date', 'hire_date', 'status')
        }),
        (_('البيانات الوظيفية'), {
            'fields': ('department', 'division', 'job_title', 'certificate',
                      'job_grade', 'job_stage')
        }),
        (_('معلومات الاتصال'), {
            'fields': ('phone', 'email', 'address')
        }),
        (_('ملاحظات'), {
            'fields': ('notes',)
        }),
    )

    readonly_fields = ['created_at', 'updated_at']

    def get_readonly_fields(self, request, obj=None):
        if obj:  # تحرير موجود
            return self.readonly_fields + ['employee_number']
        return self.readonly_fields
