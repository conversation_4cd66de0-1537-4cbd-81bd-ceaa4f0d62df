from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (Department, Section, Division, JobTitle, Certificate,
                    Degree, Stage, Employee)


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['department_number', 'name', 'phone', 'email']
    search_fields = ['department_number', 'name']
    ordering = ['department_number']


@admin.register(Section)
class SectionAdmin(admin.ModelAdmin):
    list_display = ['name', 'section_number']
    search_fields = ['name', 'section_number']
    ordering = ['name']


@admin.register(Division)
class DivisionAdmin(admin.ModelAdmin):
    list_display = ['name', 'division_number', 'section']
    search_fields = ['name', 'division_number', 'section__name']
    list_filter = ['section']
    ordering = ['section__name', 'name']


@admin.register(JobTitle)
class JobTitleAdmin(admin.ModelAdmin):
    list_display = ['name', 'title_number']
    search_fields = ['name', 'title_number']
    ordering = ['name']


@admin.register(Certificate)
class CertificateAdmin(admin.ModelAdmin):
    list_display = ['name', 'certificate_number']
    search_fields = ['name', 'certificate_number']
    ordering = ['name']


@admin.register(Degree)
class DegreeAdmin(admin.ModelAdmin):
    list_display = ['name', 'degree_number']
    search_fields = ['name', 'degree_number']
    ordering = ['name']


@admin.register(Stage)
class StageAdmin(admin.ModelAdmin):
    list_display = ['name', 'stage_number']
    search_fields = ['name', 'stage_number']
    ordering = ['name']


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['employee_number', 'full_name', 'department', 'job_title',
                   'employee_status', 'hire_date']
    search_fields = ['employee_number', 'full_name', 'iban']
    list_filter = ['employee_status', 'gender', 'marital_status', 'department',
                  'job_title', 'hire_date']
    ordering = ['employee_number']

    fieldsets = (
        (_('البيانات الأساسية'), {
            'fields': ('employee_number', 'iban', 'full_name', 'gender',
                      'marital_status', 'birth_date', 'hire_date', 'employee_status')
        }),
        (_('البيانات الوظيفية'), {
            'fields': ('department', 'section', 'division', 'job_title', 'certificate',
                      'degree', 'stage')
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # تحرير موجود
            return ['employee_number']
        return []
