@echo off
chcp 65001 >nul
title نظام إدارة الرواتب والمحاسبة - تطبيق سطح المكتب

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🏢 نظام إدارة الرواتب والمحاسبة                    ║
echo ║                                                              ║
echo ║                    تطبيق سطح المكتب                         ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 بدء تشغيل النظام...
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM تشغيل المشغل المحسن
python start_desktop.py

pause
