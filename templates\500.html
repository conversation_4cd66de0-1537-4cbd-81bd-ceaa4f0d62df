{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}500 - خطأ في الخادم{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <h1 class="display-1 text-danger mb-4">500</h1>
                <img src="{% static 'img/500.svg' %}" alt="500" class="img-fluid mb-4" style="max-width: 300px;">
                <h2 class="mb-4">عذراً، حدث خطأ في الخادم</h2>
                <p class="text-muted mb-4">
                    حدث خطأ غير متوقع في الخادم. الرجاء المحاولة مرة أخرى لاحقاً.
                    <br>
                    إذا استمرت المشكلة، يرجى الاتصال بمسؤول النظام.
                </p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="{% url 'dashboard' %}" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        الرجوع للرئيسية
                    </a>
                    <button onclick="window.location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-sync me-2"></i>
                        إعادة تحميل الصفحة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 3rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.error-page h1 {
    font-size: 6rem;
    font-weight: bold;
    color: var(--danger-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-page h2 {
    color: var(--dark-color);
    font-weight: bold;
}

.error-page .btn {
    padding: 0.75rem 1.5rem;
    font-weight: bold;
}

@media (max-width: 768px) {
    .error-page {
        padding: 2rem;
    }
    
    .error-page h1 {
        font-size: 4rem;
    }
}
</style>
{% endblock %}