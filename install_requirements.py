#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثبت المتطلبات التلقائي
يقوم بتثبيت جميع المتطلبات اللازمة للنظام
"""

import subprocess
import sys
import os

def print_banner():
    """طباعة شعار المثبت"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           🔧 مثبت متطلبات النظام التلقائي                    ║
    ║                                                              ║
    ║                  نظام إدارة الرواتب والمحاسبة               ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_package(package):
    """تثبيت حزمة واحدة"""
    try:
        print(f"📦 تثبيت {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package], 
                            capture_output=True, text=True)
        print(f"✅ تم تثبيت {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل تثبيت {package}: {e}")
        return False

def install_requirements():
    """تثبيت المتطلبات الأساسية"""
    print("📦 بدء تثبيت المتطلبات...")
    
    # المتطلبات الأساسية
    basic_requirements = [
        "Django>=4.2,<5.0",
        "PyQt5>=5.15.9",
        "pandas>=2.0.2",
        "openpyxl>=3.1.2",
        "psycopg2-binary>=2.9.5",
        "python-dateutil>=2.8.2",
        "python-dotenv>=1.0.0"
    ]
    
    success_count = 0
    total_count = len(basic_requirements)
    
    for package in basic_requirements:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 النتائج:")
    print(f"✅ تم تثبيت: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 تم تثبيت جميع المتطلبات بنجاح!")
        return True
    else:
        print("⚠️ بعض المتطلبات لم يتم تثبيتها")
        return False

def install_optional_requirements():
    """تثبيت المتطلبات الاختيارية"""
    print("\n🔧 تثبيت المتطلبات الاختيارية...")
    
    optional_requirements = [
        "arabic-reshaper>=3.0.0",
        "python-bidi>=0.4.2"
    ]
    
    for package in optional_requirements:
        install_package(package)

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص Python
    if not check_python():
        input("\nاضغط Enter للخروج...")
        return
    
    # تحديث pip
    print("🔄 تحديث pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                            capture_output=True)
        print("✅ تم تحديث pip")
    except:
        print("⚠️ تعذر تحديث pip")
    
    # تثبيت المتطلبات الأساسية
    if install_requirements():
        # تثبيت المتطلبات الاختيارية
        install_optional_requirements()
        
        print("\n" + "="*60)
        print("🎉 تم إعداد النظام بنجاح!")
        print("="*60)
        print("💡 يمكنك الآن تشغيل النظام باستخدام:")
        print("   python start_desktop.py")
        print("   أو")
        print("   python desktop_app.py")
    else:
        print("\n❌ فشل في إعداد النظام")
        print("💡 يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف التثبيت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")
    finally:
        input("\nاضغط Enter للخروج...")
