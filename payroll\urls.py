from django.urls import path
from . import views

app_name = 'payroll'

urlpatterns = [
    # الرواتب
    path('', views.PayrollListView.as_view(), name='payroll_list'),
    path('create/', views.PayrollCreateView.as_view(), name='payroll_create'),
    path('<int:pk>/', views.PayrollDetailView.as_view(), name='payroll_detail'),
    path('<int:pk>/edit/', views.PayrollUpdateView.as_view(), name='payroll_edit'),
    path('<int:pk>/delete/', views.PayrollDeleteView.as_view(), name='payroll_delete'),
    path('<int:pk>/calculate/', views.PayrollCalculateView.as_view(), name='payroll_calculate'),
    path('<int:pk>/approve/', views.PayrollApproveView.as_view(), name='payroll_approve'),
    
    # فترات الرواتب
    path('periods/', views.PayrollPeriodListView.as_view(), name='period_list'),
    path('periods/create/', views.PayrollPeriodCreateView.as_view(), name='period_create'),
    path('periods/<int:pk>/edit/', views.PayrollPeriodUpdateView.as_view(), name='period_edit'),
    path('periods/<int:pk>/delete/', views.PayrollPeriodDeleteView.as_view(), name='period_delete'),
    path('periods/<int:pk>/close/', views.PayrollPeriodCloseView.as_view(), name='period_close'),
    
    # أنواع المخصصات
    path('allowance-types/', views.AllowanceTypeListView.as_view(), name='allowance_type_list'),
    path('allowance-types/create/', views.AllowanceTypeCreateView.as_view(), name='allowance_type_create'),
    path('allowance-types/<int:pk>/edit/', views.AllowanceTypeUpdateView.as_view(), name='allowance_type_edit'),
    path('allowance-types/<int:pk>/delete/', views.AllowanceTypeDeleteView.as_view(), name='allowance_type_delete'),
    
    # أنواع الاستقطاعات
    path('deduction-types/', views.DeductionTypeListView.as_view(), name='deduction_type_list'),
    path('deduction-types/create/', views.DeductionTypeCreateView.as_view(), name='deduction_type_create'),
    path('deduction-types/<int:pk>/edit/', views.DeductionTypeUpdateView.as_view(), name='deduction_type_edit'),
    path('deduction-types/<int:pk>/delete/', views.DeductionTypeDeleteView.as_view(), name='deduction_type_delete'),
    
    # شرائح الضريبة
    path('tax-brackets/', views.TaxBracketListView.as_view(), name='tax_bracket_list'),
    path('tax-brackets/create/', views.TaxBracketCreateView.as_view(), name='tax_bracket_create'),
    path('tax-brackets/<int:pk>/edit/', views.TaxBracketUpdateView.as_view(), name='tax_bracket_edit'),
    path('tax-brackets/<int:pk>/delete/', views.TaxBracketDeleteView.as_view(), name='tax_bracket_delete'),
    
    # معالجة جماعية
    path('bulk-calculate/', views.BulkCalculateView.as_view(), name='bulk_calculate'),
    path('bulk-approve/', views.BulkApproveView.as_view(), name='bulk_approve'),
    
    # تصدير
    path('export/', views.PayrollExportView.as_view(), name='payroll_export'),
    path('export-period/<int:period_id>/', views.PayrollPeriodExportView.as_view(), name='period_export'),
]
