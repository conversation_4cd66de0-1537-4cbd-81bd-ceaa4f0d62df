# Generated by Django 4.2.7 on 2025-06-05 17:37

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("employees", "0001_initial"),
        ("accounting", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AllowanceType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="اسم المخصص"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز المخصص"
                    ),
                ),
                (
                    "is_percentage",
                    models.BooleanField(default=False, verbose_name="نسبة مئوية"),
                ),
                (
                    "default_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="المبلغ الافتراضي",
                    ),
                ),
                (
                    "is_taxable",
                    models.BooleanField(default=True, verbose_name="خاضع للضريبة"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "نوع المخصص",
                "verbose_name_plural": "أنواع المخصصات",
                "db_table": "allowance_types",
            },
        ),
        migrations.CreateModel(
            name="DeductionType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="اسم الاستقطاع"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز الاستقطاع"
                    ),
                ),
                (
                    "is_percentage",
                    models.BooleanField(default=False, verbose_name="نسبة مئوية"),
                ),
                (
                    "default_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="المبلغ الافتراضي",
                    ),
                ),
                (
                    "is_mandatory",
                    models.BooleanField(default=False, verbose_name="إجباري"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "نوع الاستقطاع",
                "verbose_name_plural": "أنواع الاستقطاعات",
                "db_table": "deduction_types",
            },
        ),
        migrations.CreateModel(
            name="TaxBracket",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الشريحة")),
                (
                    "min_amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=12, verbose_name="الحد الأدنى"
                    ),
                ),
                (
                    "max_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=12,
                        null=True,
                        verbose_name="الحد الأعلى",
                    ),
                ),
                (
                    "tax_rate",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="معدل الضريبة %",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "شريحة الضريبة",
                "verbose_name_plural": "شرائح الضريبة",
                "db_table": "tax_brackets",
                "ordering": ["min_amount"],
            },
        ),
        migrations.CreateModel(
            name="PayrollPeriod",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الفترة")),
                ("year", models.IntegerField(verbose_name="السنة")),
                (
                    "month",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                        verbose_name="الشهر",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="تاريخ البداية")),
                ("end_date", models.DateField(verbose_name="تاريخ النهاية")),
                ("is_closed", models.BooleanField(default=False, verbose_name="مقفلة")),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "فترة الرواتب",
                "verbose_name_plural": "فترات الرواتب",
                "db_table": "payroll_periods",
                "ordering": ["-year", "-month"],
                "unique_together": {("year", "month")},
            },
        ),
        migrations.CreateModel(
            name="Payroll",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "basic_salary",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=12,
                        verbose_name="الراتب الأساسي",
                    ),
                ),
                (
                    "salary_difference",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=12,
                        verbose_name="فرق راتب",
                    ),
                ),
                (
                    "position_allowance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="مخصصات المنصب",
                    ),
                ),
                (
                    "marriage_allowance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="مخصصات الزوجية",
                    ),
                ),
                (
                    "children_allowance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="مخصصات الأولاد",
                    ),
                ),
                (
                    "engineering_allowance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="مخصصات هندسية",
                    ),
                ),
                (
                    "certificate_allowance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="مخصصات الشهادة",
                    ),
                ),
                (
                    "craft_allowance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="مخصصات الحرفة",
                    ),
                ),
                (
                    "danger_allowance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="مخصصات الخطورة",
                    ),
                ),
                (
                    "location_allowance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="مخصصات الموقع الجغرافي",
                    ),
                ),
                (
                    "total_allowances",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=12,
                        verbose_name="إجمالي المخصصات",
                    ),
                ),
                (
                    "retirement_fund",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="صندوق تقاعد موظفي الدولة",
                    ),
                ),
                (
                    "government_contribution",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="حصة الدائرة في المساهمة الحكومية",
                    ),
                ),
                (
                    "income_tax",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="ضريبة الدخل",
                    ),
                ),
                (
                    "social_protection_fund",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="صندوق هيئة الحماية الاجتماعية",
                    ),
                ),
                (
                    "health_insurance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="أمانات الضمان الصحي",
                    ),
                ),
                (
                    "other_departments",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="دوائر وجهات أخرى",
                    ),
                ),
                (
                    "salary_difference_deduction",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="فرق راتب (استقطاع)",
                    ),
                ),
                (
                    "execution_departments",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="دوائر التنفيذ",
                    ),
                ),
                (
                    "bank_installments",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=10,
                        verbose_name="أقساط المصارف",
                    ),
                ),
                (
                    "total_deductions",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=12,
                        verbose_name="إجمالي الاستقطاعات",
                    ),
                ),
                (
                    "net_salary",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        max_digits=12,
                        verbose_name="صافي الراتب",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "مسودة"),
                            ("calculated", "محسوب"),
                            ("approved", "معتمد"),
                            ("paid", "مدفوع"),
                            ("cancelled", "ملغي"),
                        ],
                        default="draft",
                        max_length=15,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "calculated_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الحساب"
                    ),
                ),
                (
                    "approved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الاعتماد"
                    ),
                ),
                (
                    "paid_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الدفع"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "bank_account",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="accounting.bankaccount",
                        verbose_name="الحساب البنكي",
                    ),
                ),
                (
                    "cost_center",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="accounting.costcenter",
                        verbose_name="مركز التكلفة",
                    ),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.employee",
                        verbose_name="الموظف",
                    ),
                ),
                (
                    "period",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="payroll.payrollperiod",
                        verbose_name="فترة الرواتب",
                    ),
                ),
            ],
            options={
                "verbose_name": "مسير الرواتب",
                "verbose_name_plural": "مسيرات الرواتب",
                "db_table": "payrolls",
                "ordering": [
                    "-period__year",
                    "-period__month",
                    "employee__employee_number",
                ],
                "unique_together": {("employee", "period")},
            },
        ),
    ]
