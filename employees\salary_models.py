from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from .models import Employee

class BaseSalary(models.Model):
    employee = models.OneToOneField(
        Employee,
        on_delete=models.CASCADE,
        verbose_name=_('الموظف')
    )
    base_salary = models.DecimalField(
        _('الراتب الجديد'),
        max_digits=12,
        decimal_places=3
    )
    government_contribution = models.DecimalField(
        _('حصة الدائرة في المساهمة الحكومية'),
        max_digits=12,
        decimal_places=3,
        help_text=_('15% من الراتب الأساسي')
    )
    salary_difference = models.DecimalField(
        _('فرق راتب'),
        max_digits=12,
        decimal_places=3,
        default=0
    )

    class Meta:
        verbose_name = _('الراتب الأساسي')
        verbose_name_plural = _('الرواتب الأساسية')

    def __str__(self):
        return f"راتب {self.employee.full_name}"

    def save(self, *args, **kwargs):
        # حساب المساهمة الحكومية تلقائياً (15%)
        self.government_contribution = self.base_salary * Decimal('0.15')
        super().save(*args, **kwargs)

class Allowance(models.Model):
    employee = models.OneToOneField(
        Employee,
        on_delete=models.CASCADE,
        verbose_name=_('الموظف')
    )
    position_allowance = models.DecimalField(
        _('مخصصات المنصب'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    marriage_allowance = models.DecimalField(
        _('مخصصات الزوجية'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    children_allowance = models.DecimalField(
        _('مخصصات الأولاد'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    engineering_allowance = models.DecimalField(
        _('مخصصات هندسية'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    certificate_allowance = models.DecimalField(
        _('مخصصات الشهادة'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    craft_allowance = models.DecimalField(
        _('مخصصات الحرفة'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    risk_allowance = models.DecimalField(
        _('مخصصات الخطورة'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    location_allowance = models.DecimalField(
        _('مخصصات الموقع الجغرافي'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    total_allowances = models.DecimalField(
        _('إجمالي المخصصات'),
        max_digits=12,
        decimal_places=3,
        default=0
    )

    class Meta:
        verbose_name = _('المخصصات')
        verbose_name_plural = _('المخصصات')

    def __str__(self):
        return f"مخصصات {self.employee.full_name}"

    def calculate_total(self):
        """حساب إجمالي المخصصات"""
        fields = [
            'position_allowance', 'marriage_allowance', 'children_allowance',
            'engineering_allowance', 'certificate_allowance', 'craft_allowance',
            'risk_allowance', 'location_allowance'
        ]
        return sum(getattr(self, field) for field in fields)

    def save(self, *args, **kwargs):
        self.total_allowances = self.calculate_total()
        super().save(*args, **kwargs)

class Deduction(models.Model):
    employee = models.OneToOneField(
        Employee,
        on_delete=models.CASCADE,
        verbose_name=_('الموظف')
    )
    retirement_fund = models.DecimalField(
        _('صندوق تقاعد موظفي الدولة'),
        max_digits=12,
        decimal_places=3,
        help_text=_('10% من الراتب الأساسي')
    )
    government_contribution = models.DecimalField(
        _('حصة الدائرة في المساهمة الحكومية'),
        max_digits=12,
        decimal_places=3,
        help_text=_('15% من الراتب الأساسي')
    )
    income_tax = models.DecimalField(
        _('ضريبة الدخل'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    social_security_fund = models.DecimalField(
        _('صندوق هيئة الحماية الاجتماعية'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    health_insurance = models.DecimalField(
        _('أمانات الضمان الصحي'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    other_departments = models.DecimalField(
        _('دوائر وجهات أخرى'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    salary_difference = models.DecimalField(
        _('فرق راتب'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    execution_departments = models.DecimalField(
        _('دوائر التنفيذ'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    bank_installments = models.DecimalField(
        _('أقساط المصارف'),
        max_digits=12,
        decimal_places=3,
        default=0
    )
    total_deductions = models.DecimalField(
        _('إجمالي الاستقطاعات'),
        max_digits=12,
        decimal_places=3,
        default=0
    )

    class Meta:
        verbose_name = _('الاستقطاعات')
        verbose_name_plural = _('الاستقطاعات')

    def __str__(self):
        return f"استقطاعات {self.employee.full_name}"

    def calculate_total(self):
        """حساب إجمالي الاستقطاعات"""
        fields = [
            'retirement_fund', 'government_contribution', 'income_tax',
            'social_security_fund', 'health_insurance', 'other_departments',
            'salary_difference', 'execution_departments', 'bank_installments'
        ]
        return sum(getattr(self, field) for field in fields)

    def save(self, *args, **kwargs):
        self.total_deductions = self.calculate_total()
        super().save(*args, **kwargs)

class SalarySheet(models.Model):
    employee = models.ForeignKey(
        Employee,
        on_delete=models.PROTECT,
        verbose_name=_('الموظف')
    )
    month = models.PositiveSmallIntegerField(
        _('الشهر'),
        validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    year = models.PositiveIntegerField(_('السنة'))
    base_salary = models.DecimalField(
        _('الراتب الأساسي'),
        max_digits=12,
        decimal_places=3
    )
    total_allowances = models.DecimalField(
        _('إجمالي المخصصات'),
        max_digits=12,
        decimal_places=3
    )
    total_deductions = models.DecimalField(
        _('إجمالي الاستقطاعات'),
        max_digits=12,
        decimal_places=3
    )
    net_salary = models.DecimalField(
        _('صافي الراتب'),
        max_digits=12,
        decimal_places=3
    )
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('كشف الراتب')
        verbose_name_plural = _('كشوفات الرواتب')
        unique_together = ['employee', 'month', 'year']
        ordering = ['-year', '-month']

    def __str__(self):
        return f"راتب {self.employee.full_name} - {self.month}/{self.year}"

    def calculate_net_salary(self):
        """حساب صافي الراتب"""
        return self.base_salary + self.total_allowances - self.total_deductions

    def save(self, *args, **kwargs):
        self.net_salary = self.calculate_net_salary()
        super().save(*args, **kwargs)