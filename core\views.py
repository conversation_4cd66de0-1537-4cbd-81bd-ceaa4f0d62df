from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils import timezone
from django.db.models import Sum
from employees.models import Employee
from employees.salary_models import SalarySheet, Allowance, Deduction
from .models import Organization
from .activity_models import Activity, SystemLog
import json

class DashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Current date
        context['current_date'] = timezone.now()
        
        # Quick stats
        context['total_employees'] = Employee.objects.count()
        
        # Get current month salary data
        current_month = timezone.now().month
        current_year = timezone.now().year
        
        current_month_salaries = SalarySheet.objects.filter(
            month=current_month,
            year=current_year
        )
        
        context['total_salaries'] = current_month_salaries.aggregate(
            Sum('net_salary')
        )['net_salary__sum'] or 0
        
        context['total_allowances'] = current_month_salaries.aggregate(
            Sum('total_allowances')
        )['total_allowances__sum'] or 0
        
        context['total_deductions'] = current_month_salaries.aggregate(
            Sum('total_deductions')
        )['total_deductions__sum'] or 0
        
        # Recent activities (last 10)
        context['recent_activities'] = Activity.objects.select_related(
            'user'
        ).order_by('-created_at')[:10]
        
        return context
