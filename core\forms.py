from django import forms
from django.utils.translation import gettext_lazy as _
from .models import FinancialTransaction, AccountGuide, BankAccount
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, HTML

class TransactionForm(forms.ModelForm):
    class Meta:
        model = FinancialTransaction
        fields = [
            'transaction_number',
            'transaction_date',
            'transaction_type',
            'amount',
            'description',
            'bank_account',
            'account',
            'notes'
        ]
        widgets = {
            'transaction_date': forms.DateInput(attrs={'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 3}),
            'notes': forms.Textarea(attrs={'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_tag = True
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-2'
        self.helper.field_class = 'col-lg-8'
        
        self.helper.layout = Layout(
            Row(
                Column('transaction_number', css_class='col-md-6'),
                Column('transaction_date', css_class='col-md-6'),
            ),
            Row(
                Column('transaction_type', css_class='col-md-6'),
                Column('amount', css_class='col-md-6'),
            ),
            'description',
            Row(
                Column('bank_account', css_class='col-md-6'),
                Column('account', css_class='col-md-6'),
            ),
            'notes',
            HTML("<hr>"),
            Submit('submit', _('حفظ'), css_class='btn-primary'),
            HTML("<a href='{% url \"core:transaction_list\" %}' class='btn btn-secondary ms-2'>" + str(_('إلغاء')) + "</a>")
        )

    def clean_transaction_number(self):
        number = self.cleaned_data['transaction_number']
        transaction_type = self.cleaned_data.get('transaction_type')
        
        # التحقق من عدم تكرار رقم المعاملة لنفس النوع
        if FinancialTransaction.objects.filter(
            transaction_number=number,
            transaction_type=transaction_type
        ).exists():
            raise forms.ValidationError(
                _('يوجد معاملة أخرى بنفس الرقم من نفس النوع')
            )
        
        return number

    def clean(self):
        cleaned_data = super().clean()
        bank_account = cleaned_data.get('bank_account')
        amount = cleaned_data.get('amount')
        transaction_type = cleaned_data.get('transaction_type')
        
        if all([bank_account, amount, transaction_type]):
            # التحقق من كفاية الرصيد في حالة سند الصرف
            if transaction_type == 'payment' and amount > bank_account.current_balance:
                self.add_error(
                    'amount',
                    _('الرصيد الحالي غير كافي لإجراء هذه العملية')
                )
        
        return cleaned_data

class DateRangeForm(forms.Form):
    start_date = forms.DateField(
        label=_('من تاريخ'),
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    end_date = forms.DateField(
        label=_('إلى تاريخ'),
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'form-inline'
        
        self.helper.layout = Layout(
            Row(
                Column('start_date', css_class='col-md-5'),
                Column('end_date', css_class='col-md-5'),
                Column(
                    Submit('submit', _('عرض'), css_class='btn-primary'),
                    css_class='col-md-2'
                ),
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError(
                _('تاريخ البداية يجب أن يكون قبل تاريخ النهاية')
            )
        
        return cleaned_data

class AccountBalanceForm(forms.Form):
    account = forms.ModelChoiceField(
        queryset=AccountGuide.objects.all(),
        label=_('الحساب'),
        empty_label=_('اختر الحساب')
    )
    start_date = forms.DateField(
        label=_('من تاريخ'),
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    end_date = forms.DateField(
        label=_('إلى تاريخ'),
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        
        self.helper.layout = Layout(
            Row(
                Column('account', css_class='col-md-4'),
                Column('start_date', css_class='col-md-4'),
                Column('end_date', css_class='col-md-4'),
            ),
            Submit('submit', _('عرض الكشف'), css_class='btn-primary mt-3')
        )