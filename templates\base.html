{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}المحاسب الشامل{% endblock %}</title>
    
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        {% if user.is_authenticated %}
        <nav class="sidebar">
            <div class="sidebar-header mb-4">
                <h3 class="text-center">المحاسب الشامل</h3>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="{% url 'dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'user_list' %}" class="nav-link {% if 'user' in request.path %}active{% endif %}">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'settings' %}" class="nav-link {% if 'settings' in request.path %}active{% endif %}">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات والتهيئة
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'employee_list' %}" class="nav-link {% if 'employee' in request.path %}active{% endif %}">
                        <i class="fas fa-user-tie me-2"></i>
                        شؤون الموظفين
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'account_list' %}" class="nav-link {% if 'account' in request.path %}active{% endif %}">
                        <i class="fas fa-money-check-alt me-2"></i>
                        الحسابات
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'salary_list' %}" class="nav-link {% if 'salary' in request.path %}active{% endif %}">
                        <i class="fas fa-dollar-sign me-2"></i>
                        الرواتب
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'report_list' %}" class="nav-link {% if 'report' in request.path %}active{% endif %}">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير
                    </a>
                </li>
            </ul>
        </nav>
        {% endif %}

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
                <div class="container-fluid">
                    {% if user.is_authenticated %}
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-2"></i>{{ user.get_full_name|default:user.username }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="{% url 'profile' %}">الملف الشخصي</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'logout' %}">تسجيل الخروج</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </nav>

            <!-- Messages -->
            {% if messages %}
            <div class="container">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Content -->
            <div class="container">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>