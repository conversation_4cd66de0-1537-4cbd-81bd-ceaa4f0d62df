# Generated by Django 5.2.2 on 2025-06-06 10:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0002_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="accountguide",
            name="parent",
        ),
        migrations.RemoveField(
            model_name="financialtransaction",
            name="account",
        ),
        migrations.DeleteModel(
            name="AccountingPeriod",
        ),
        migrations.RemoveField(
            model_name="activity",
            name="user",
        ),
        migrations.RemoveField(
            model_name="bankaccount",
            name="bank",
        ),
        migrations.RemoveField(
            model_name="bankbranch",
            name="bank",
        ),
        migrations.RemoveField(
            model_name="bankaccount",
            name="currency",
        ),
        migrations.RemoveField(
            model_name="bankbranch",
            name="bank_account",
        ),
        migrations.RemoveField(
            model_name="financialtransaction",
            name="bank_account",
        ),
        migrations.DeleteModel(
            name="FinalAccount",
        ),
        migrations.DeleteModel(
            name="Fund",
        ),
        migrations.DeleteModel(
            name="SystemLog",
        ),
        migrations.AlterModelOptions(
            name="organization",
            options={"verbose_name": "المؤسسة", "verbose_name_plural": "المؤسسة"},
        ),
        migrations.RemoveField(
            model_name="organization",
            name="address",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="logo",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="notes",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="organization_number",
        ),
        migrations.AlterField(
            model_name="organization",
            name="email",
            field=models.EmailField(
                blank=True, max_length=254, verbose_name="البريد الإلكتروني"
            ),
        ),
        migrations.AlterField(
            model_name="organization",
            name="name",
            field=models.CharField(
                default="شركة النظام", max_length=255, verbose_name="اسم المؤسسة"
            ),
        ),
        migrations.DeleteModel(
            name="AccountGuide",
        ),
        migrations.DeleteModel(
            name="Activity",
        ),
        migrations.DeleteModel(
            name="Bank",
        ),
        migrations.DeleteModel(
            name="Currency",
        ),
        migrations.DeleteModel(
            name="BankBranch",
        ),
        migrations.DeleteModel(
            name="BankAccount",
        ),
        migrations.DeleteModel(
            name="FinancialTransaction",
        ),
    ]
