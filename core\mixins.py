from django.contrib.auth.mixins import UserPassesTestMixin
from django.core.exceptions import PermissionDenied
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from .activity_models import Activity, SystemLog
import json

class ActivityTrackingMixin:
    """وعاء لتتبع نشاطات المستخدمين في العروض"""
    
    def create_activity(self, action, details=''):
        """تسجيل نشاط جديد"""
        return Activity.log_activity(
            user=self.request.user,
            action=action,
            details=details,
            request=self.request
        )

class LoggingMixin:
    """وعاء لتسجيل الأحداث في سجل النظام"""
    
    def log_info(self, message):
        return SystemLog.info(message)
    
    def log_warning(self, message):
        return SystemLog.warning(message)
    
    def log_error(self, message, traceback=''):
        return SystemLog.error(message, traceback)
    
    def log_critical(self, message, traceback=''):
        return SystemLog.critical(message, traceback)

class PermissionRequiredMixin(UserPassesTestMixin):
    """وعاء للتحقق من صلاحيات المستخدم"""
    
    permission_required = None
    
    def test_func(self):
        if self.permission_required is None:
            return True
        
        # تحويل الصلاحية المطلوبة إلى قائمة إذا كانت نصاً
        if isinstance(self.permission_required, str):
            perms = (self.permission_required,)
        else:
            perms = self.permission_required
        
        # التحقق من امتلاك المستخدم لجميع الصلاحيات المطلوبة
        return self.request.user.has_perms(perms)
    
    def handle_no_permission(self):
        messages.error(
            self.request,
            _('ليس لديك الصلاحية للقيام بهذا الإجراء')
        )
        raise PermissionDenied

class JSONResponseMixin:
    """وعاء لإرجاع استجابات JSON"""
    
    def render_to_json_response(self, context, **response_kwargs):
        return JsonResponse(
            self.get_data(context),
            **response_kwargs
        )
    
    def get_data(self, context):
        return context

class AuditableMixin:
    """وعاء لتتبع التغييرات على النماذج"""
    
    def save(self, *args, **kwargs):
        if not self.pk:  # إذا كان كائناً جديداً
            self.created_by = kwargs.pop('user', None)
        else:  # إذا كان تحديثاً لكائن موجود
            self.modified_by = kwargs.pop('user', None)
            
            # تسجيل التغييرات
            if hasattr(self, '_loaded_values'):
                changes = {}
                for field, value in self._loaded_values.items():
                    if getattr(self, field) != value:
                        changes[field] = {
                            'old': value,
                            'new': getattr(self, field)
                        }
                
                if changes:
                    Activity.log_activity(
                        user=self.modified_by,
                        action=f'تحديث {self._meta.verbose_name}',
                        details=json.dumps(changes, ensure_ascii=False)
                    )
        
        super().save(*args, **kwargs)
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # حفظ القيم الأصلية للحقول
        self._loaded_values = {
            field.name: getattr(self, field.name)
            for field in self._meta.fields
        }

class PrintableMixin:
    """وعاء لإضافة وظائف الطباعة للنماذج"""
    
    def get_print_context(self):
        """إرجاع السياق المطلوب للطباعة"""
        return {
            'title': str(self),
            'object': self,
            'organization': self.get_organization(),
        }
    
    def get_organization(self):
        """إرجاع معلومات المؤسسة"""
        from .models import Organization
        return Organization.objects.first()
    
    def get_print_template(self):
        """إرجاع قالب الطباعة"""
        return f"print/{self._meta.model_name}.html"

class FilterMixin:
    """وعاء لإضافة وظائف التصفية للعروض"""
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # تطبيق التصفية من النموذج
        form = self.get_filter_form()
        if form.is_valid():
            return form.filter_queryset(queryset)
        
        return queryset
    
    def get_filter_form(self):
        """إرجاع نموذج التصفية"""
        return self.filter_form_class(self.request.GET)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter_form'] = self.get_filter_form()
        return context