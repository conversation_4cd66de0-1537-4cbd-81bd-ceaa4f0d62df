# Generated by Django 4.2.7 on 2025-06-06 08:31

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Allowance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "position_allowance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="مخصصات المنصب",
                    ),
                ),
                (
                    "marriage_allowance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="مخصصات الزوجية",
                    ),
                ),
                (
                    "children_allowance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="مخصصات الأولاد",
                    ),
                ),
                (
                    "engineering_allowance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="مخصصات هندسية",
                    ),
                ),
                (
                    "certificate_allowance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="مخصصات الشهادة",
                    ),
                ),
                (
                    "craft_allowance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="مخصصات الحرفة",
                    ),
                ),
                (
                    "risk_allowance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="مخصصات الخطورة",
                    ),
                ),
                (
                    "location_allowance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="مخصصات الموقع الجغرافي",
                    ),
                ),
                (
                    "total_allowances",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="إجمالي المخصصات",
                    ),
                ),
            ],
            options={
                "verbose_name": "المخصصات",
                "verbose_name_plural": "المخصصات",
            },
        ),
        migrations.CreateModel(
            name="BaseSalary",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "base_salary",
                    models.DecimalField(
                        decimal_places=3, max_digits=12, verbose_name="الراتب الجديد"
                    ),
                ),
                (
                    "government_contribution",
                    models.DecimalField(
                        decimal_places=3,
                        help_text="15% من الراتب الأساسي",
                        max_digits=12,
                        verbose_name="حصة الدائرة في المساهمة الحكومية",
                    ),
                ),
                (
                    "salary_difference",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="فرق راتب",
                    ),
                ),
            ],
            options={
                "verbose_name": "الراتب الأساسي",
                "verbose_name_plural": "الرواتب الأساسية",
            },
        ),
        migrations.CreateModel(
            name="Certificate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "certificate_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الشهادة"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الشهادة")),
            ],
            options={
                "verbose_name": "الشهادة",
                "verbose_name_plural": "الشهادات",
            },
        ),
        migrations.CreateModel(
            name="Deduction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "retirement_fund",
                    models.DecimalField(
                        decimal_places=3,
                        help_text="10% من الراتب الأساسي",
                        max_digits=12,
                        verbose_name="صندوق تقاعد موظفي الدولة",
                    ),
                ),
                (
                    "government_contribution",
                    models.DecimalField(
                        decimal_places=3,
                        help_text="15% من الراتب الأساسي",
                        max_digits=12,
                        verbose_name="حصة الدائرة في المساهمة الحكومية",
                    ),
                ),
                (
                    "income_tax",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="ضريبة الدخل",
                    ),
                ),
                (
                    "social_security_fund",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="صندوق هيئة الحماية الاجتماعية",
                    ),
                ),
                (
                    "health_insurance",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="أمانات الضمان الصحي",
                    ),
                ),
                (
                    "other_departments",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="دوائر وجهات أخرى",
                    ),
                ),
                (
                    "salary_difference",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="فرق راتب",
                    ),
                ),
                (
                    "execution_departments",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="دوائر التنفيذ",
                    ),
                ),
                (
                    "bank_installments",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="أقساط المصارف",
                    ),
                ),
                (
                    "total_deductions",
                    models.DecimalField(
                        decimal_places=3,
                        default=0,
                        max_digits=12,
                        verbose_name="إجمالي الاستقطاعات",
                    ),
                ),
            ],
            options={
                "verbose_name": "الاستقطاعات",
                "verbose_name_plural": "الاستقطاعات",
            },
        ),
        migrations.CreateModel(
            name="Degree",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "degree_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الدرجة"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الدرجة")),
            ],
            options={
                "verbose_name": "الدرجة الوظيفية",
                "verbose_name_plural": "الدرجات الوظيفية",
            },
        ),
        migrations.CreateModel(
            name="Division",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "division_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الشعبة"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الشعبة")),
            ],
            options={
                "verbose_name": "الشعبة",
                "verbose_name_plural": "الشعب",
            },
        ),
        migrations.CreateModel(
            name="Employee",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "employee_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="الرقم الوظيفي"
                    ),
                ),
                (
                    "iban",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم الآيبان"
                    ),
                ),
                (
                    "full_name",
                    models.CharField(max_length=200, verbose_name="الاسم الرباعي"),
                ),
                (
                    "gender",
                    models.CharField(
                        choices=[("M", "ذكر"), ("F", "أنثى")],
                        max_length=1,
                        verbose_name="الجنس",
                    ),
                ),
                (
                    "marital_status",
                    models.CharField(
                        choices=[
                            ("single", "أعزب/عزباء"),
                            ("married", "متزوج/ة"),
                            ("divorced", "مطلق/ة"),
                            ("widowed", "أرمل/ة"),
                        ],
                        max_length=10,
                        verbose_name="الحالة الزوجية",
                    ),
                ),
                ("birth_date", models.DateField(verbose_name="تاريخ الميلاد")),
                ("hire_date", models.DateField(verbose_name="تاريخ التعيين")),
                (
                    "employee_status",
                    models.CharField(
                        choices=[
                            ("active", "مستمر"),
                            ("retired", "متقاعد"),
                            ("deceased", "متوفي"),
                            ("transferred", "نقل"),
                            ("suspended", "متوقف"),
                        ],
                        default="active",
                        max_length=15,
                        verbose_name="حالة الموظف",
                    ),
                ),
                (
                    "certificate",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.certificate",
                        verbose_name="الشهادة",
                    ),
                ),
                (
                    "degree",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.degree",
                        verbose_name="الدرجة الوظيفية",
                    ),
                ),
            ],
            options={
                "verbose_name": "الموظف",
                "verbose_name_plural": "الموظفون",
            },
        ),
        migrations.CreateModel(
            name="JobTitle",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم العنوان الوظيفي"
                    ),
                ),
                (
                    "name",
                    models.CharField(max_length=100, verbose_name="المسمى الوظيفي"),
                ),
            ],
            options={
                "verbose_name": "العنوان الوظيفي",
                "verbose_name_plural": "العناوين الوظيفية",
            },
        ),
        migrations.CreateModel(
            name="Section",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "section_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم القسم"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم القسم")),
            ],
            options={
                "verbose_name": "القسم",
                "verbose_name_plural": "الأقسام",
            },
        ),
        migrations.CreateModel(
            name="Stage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "stage_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم المرحلة"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم المرحلة")),
            ],
            options={
                "verbose_name": "المرحلة",
                "verbose_name_plural": "المراحل",
            },
        ),
        migrations.CreateModel(
            name="SalarySheet",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "month",
                    models.PositiveSmallIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                        verbose_name="الشهر",
                    ),
                ),
                ("year", models.PositiveIntegerField(verbose_name="السنة")),
                (
                    "base_salary",
                    models.DecimalField(
                        decimal_places=3, max_digits=12, verbose_name="الراتب الأساسي"
                    ),
                ),
                (
                    "total_allowances",
                    models.DecimalField(
                        decimal_places=3, max_digits=12, verbose_name="إجمالي المخصصات"
                    ),
                ),
                (
                    "total_deductions",
                    models.DecimalField(
                        decimal_places=3,
                        max_digits=12,
                        verbose_name="إجمالي الاستقطاعات",
                    ),
                ),
                (
                    "net_salary",
                    models.DecimalField(
                        decimal_places=3, max_digits=12, verbose_name="صافي الراتب"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "employee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.employee",
                        verbose_name="الموظف",
                    ),
                ),
            ],
            options={
                "verbose_name": "كشف الراتب",
                "verbose_name_plural": "كشوفات الرواتب",
                "ordering": ["-year", "-month"],
            },
        ),
    ]
