# Generated by Django 4.2.7 on 2025-06-05 17:37

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Certificate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="اسم الشهادة"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز الشهادة"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "الشهادة",
                "verbose_name_plural": "الشهادات",
                "db_table": "certificates",
            },
        ),
        migrations.CreateModel(
            name="Department",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="اسم القسم"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز القسم"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "القسم",
                "verbose_name_plural": "الأقسام",
                "db_table": "departments",
            },
        ),
        migrations.CreateModel(
            name="Division",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم الشعبة")),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز الشعبة"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "department",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="employees.department",
                        verbose_name="القسم",
                    ),
                ),
            ],
            options={
                "verbose_name": "الشعبة",
                "verbose_name_plural": "الشعب",
                "db_table": "divisions",
            },
        ),
        migrations.CreateModel(
            name="JobGrade",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="الدرجة الوظيفية"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز الدرجة"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "الدرجة الوظيفية",
                "verbose_name_plural": "الدرجات الوظيفية",
                "db_table": "job_grades",
            },
        ),
        migrations.CreateModel(
            name="JobStage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="المرحلة"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز المرحلة"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "المرحلة",
                "verbose_name_plural": "المراحل",
                "db_table": "job_stages",
            },
        ),
        migrations.CreateModel(
            name="JobTitle",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="العنوان الوظيفي"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=10, unique=True, verbose_name="رمز العنوان"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "العنوان الوظيفي",
                "verbose_name_plural": "العناوين الوظيفية",
                "db_table": "job_titles",
            },
        ),
        migrations.CreateModel(
            name="Employee",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "employee_number",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="الرقم الوظيفي"
                    ),
                ),
                (
                    "iban_number",
                    models.CharField(
                        max_length=34,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                "^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$"
                            )
                        ],
                        verbose_name="رقم الآيبان",
                    ),
                ),
                (
                    "full_name",
                    models.CharField(max_length=200, verbose_name="الاسم الرباعي"),
                ),
                (
                    "gender",
                    models.CharField(
                        choices=[("male", "ذكر"), ("female", "أنثى")],
                        max_length=10,
                        verbose_name="الجنس",
                    ),
                ),
                (
                    "marital_status",
                    models.CharField(
                        choices=[
                            ("single", "أعزب"),
                            ("married", "متزوج"),
                            ("divorced", "مطلق"),
                            ("widowed", "أرمل"),
                        ],
                        max_length=10,
                        verbose_name="الحالة الزوجية",
                    ),
                ),
                ("birth_date", models.DateField(verbose_name="تاريخ الميلاد")),
                ("hire_date", models.DateField(verbose_name="تاريخ التعيين")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "نشط"),
                            ("inactive", "غير نشط"),
                            ("retired", "متقاعد"),
                            ("terminated", "منتهي الخدمة"),
                        ],
                        default="active",
                        max_length=15,
                        verbose_name="حالة الموظف",
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="رقم الهاتف"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        max_length=254,
                        null=True,
                        verbose_name="البريد الإلكتروني",
                    ),
                ),
                (
                    "address",
                    models.TextField(blank=True, null=True, verbose_name="العنوان"),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="الملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "certificate",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.certificate",
                        verbose_name="الشهادة",
                    ),
                ),
                (
                    "department",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.department",
                        verbose_name="القسم",
                    ),
                ),
                (
                    "division",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.division",
                        verbose_name="الشعبة",
                    ),
                ),
                (
                    "job_grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.jobgrade",
                        verbose_name="الدرجة الوظيفية",
                    ),
                ),
                (
                    "job_stage",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.jobstage",
                        verbose_name="المرحلة",
                    ),
                ),
                (
                    "job_title",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="employees.jobtitle",
                        verbose_name="العنوان الوظيفي",
                    ),
                ),
            ],
            options={
                "verbose_name": "الموظف",
                "verbose_name_plural": "الموظفون",
                "db_table": "employees",
                "ordering": ["employee_number"],
            },
        ),
    ]
