/* Auth Pages Styles */

/* Login Page */
.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.login-logo {
    max-width: 120px;
    margin-bottom: 1.5rem;
}

.login-title {
    color: var(--primary-color);
    font-weight: bold;
    margin-bottom: 2rem;
}

/* Profile Page */
.profile-header {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    padding: 2rem 0;
    margin-bottom: 2rem;
    color: white;
}

.profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

.profile-stats {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.profile-stat-item {
    text-align: center;
    padding: 1rem;
}

.profile-stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.profile-stat-label {
    color: var(--text-color);
    font-size: 0.9rem;
}

/* Password Change Form */
.password-requirements {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1.5rem;
}

.password-requirements ul {
    list-style: none;
    padding-right: 0;
}

.password-requirements li {
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.password-requirements i {
    color: var(--success-color);
    margin-left: 0.5rem;
}

/* Activity List */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background-color 0.3s;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-timestamp {
    color: #6c757d;
    font-size: 0.9rem;
}

.activity-action {
    color: var(--primary-color);
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-card {
        margin: 1rem;
    }
    
    .profile-avatar {
        width: 100px;
        height: 100px;
    }
    
    .profile-stat-value {
        font-size: 1.2rem;
    }
}