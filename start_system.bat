@echo off
chcp 65001 >nul
title النظام المحاسبي الموحد - رواتب الموظفين

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    النظام المحاسبي الموحد                    ║
echo ║                      رواتب الموظفين                        ║
echo ║                                                              ║
echo ║                      الإصدار 1.0                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM فحص وجود ملف requirements.txt
if not exist "requirements.txt" (
    echo ❌ خطأ: ملف requirements.txt غير موجود
    pause
    exit /b 1
)

REM عرض القائمة
:menu
echo 📋 اختر طريقة التشغيل:
echo 1. تثبيت المتطلبات وإعداد النظام
echo 2. تشغيل خادم الويب (Django)
echo 3. تشغيل تطبيق سطح المكتب (PyQt5)
echo 4. تشغيل كلاهما
echo 5. إنشاء مستخدم إداري
echo 6. إعداد البيانات الأولية
echo 7. إنشاء نسخة احتياطية
echo 8. خروج
echo.

set /p choice="اختر رقم الخيار (1-8): "

if "%choice%"=="1" goto install
if "%choice%"=="2" goto web
if "%choice%"=="3" goto desktop
if "%choice%"=="4" goto both
if "%choice%"=="5" goto superuser
if "%choice%"=="6" goto initial_data
if "%choice%"=="7" goto backup
if "%choice%"=="8" goto exit
echo ❌ خيار غير صحيح، يرجى المحاولة مرة أخرى
echo.
goto menu

:install
echo.
echo 📦 تثبيت المتطلبات وإعداد النظام...
echo.

REM تثبيت المتطلبات
echo 🔄 تثبيت المتطلبات...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    goto menu
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.

REM إعداد قاعدة البيانات
echo 🗄️ إعداد قاعدة البيانات...
python manage.py makemigrations
if errorlevel 1 (
    echo ❌ فشل في إنشاء migrations
    pause
    goto menu
)

python manage.py migrate
if errorlevel 1 (
    echo ❌ فشل في تطبيق migrations
    pause
    goto menu
)

echo ✅ تم إعداد قاعدة البيانات بنجاح
echo.

REM جمع الملفات الثابتة
echo 📁 جمع الملفات الثابتة...
python manage.py collectstatic --noinput
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في جمع الملفات الثابتة
)

echo ✅ تم إعداد النظام بنجاح!
echo.
pause
goto menu

:web
echo.
echo 🌐 تشغيل خادم الويب...
echo 📍 الرابط: http://127.0.0.1:8000
echo 🛑 اضغط Ctrl+C للإيقاف
echo.
python manage.py runserver 127.0.0.1:8000
pause
goto menu

:desktop
echo.
echo 🖥️ تشغيل تطبيق سطح المكتب...
echo.
python desktop_app.py
pause
goto menu

:both
echo.
echo 🚀 تشغيل كلا التطبيقين...
echo 📍 خادم الويب: http://127.0.0.1:8000
echo.

REM تشغيل خادم Django في الخلفية
start "Django Server" cmd /c "python manage.py runserver 127.0.0.1:8000"

REM انتظار قليل ثم تشغيل تطبيق سطح المكتب
timeout /t 3 /nobreak >nul
python desktop_app.py

pause
goto menu

:superuser
echo.
echo 👤 إنشاء مستخدم إداري...
echo.
python manage.py createsuperuser
pause
goto menu

:initial_data
echo.
echo 📊 إعداد البيانات الأولية...
echo.
python manage.py setup_initial_data
if errorlevel 1 (
    echo ❌ فشل في إعداد البيانات الأولية
) else (
    echo ✅ تم إعداد البيانات الأولية بنجاح
)
pause
goto menu

:backup
echo.
echo 💾 إنشاء نسخة احتياطية...
echo.

REM إنشاء مجلد النسخ الاحتياطية
if not exist "backups" mkdir backups

REM إنشاء اسم الملف مع التاريخ والوقت
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

set "backup_file=backups\payroll_backup_%datestamp%.json"

python manage.py dumpdata --natural-foreign --natural-primary --exclude=contenttypes --exclude=auth.permission > "%backup_file%"
if errorlevel 1 (
    echo ❌ فشل في إنشاء النسخة الاحتياطية
) else (
    echo ✅ تم إنشاء النسخة الاحتياطية: %backup_file%
)
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام النظام المحاسبي الموحد!
echo.
pause
exit /b 0
