# Django settings
DEBUG=True
SECRET_KEY=change-this-to-a-secure-secret-key
ALLOWED_HOSTS=localhost,127.0.0.1

# Database settings
DB_ENGINE=django.db.backends.postgresql
DB_NAME=accounting_db
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# Email settings
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Security settings
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE=5242880

# Locale settings
LANGUAGE_CODE=ar
TIME_ZONE=Asia/Baghdad

# Cache settings
CACHE_BACKEND=django.core.cache.backends.filebased.FileBasedCache
CACHE_LOCATION=cache

# Backup settings
BACKUP_RETENTION_DAYS=30

# System settings
MAINTENANCE_MODE=False

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/debug.log

# Custom settings
ORGANIZATION_NAME=المحاسب الشامل
ORGANIZATION_WEBSITE=https://accountant.example.com