from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.template.loader import render_to_string
from django.conf import settings
from django.db.models import Sum, Count
from core.models import Organization
from core.financial_models import (
    FinancialTransaction, AccountGuide, BankAccount
)
from employees.models import Employee
from employees.salary_models import SalarySheet
import os
import weasyprint
import calendar
from datetime import date

class Command(BaseCommand):
    help = 'توليد التقارير المالية'

    def add_arguments(self, parser):
        parser.add_argument(
            'report_type',
            type=str,
            choices=[
                'daily_journal',
                'account_statement',
                'salary_report',
                'balance_sheet',
                'trial_balance',
                'financial_position'
            ],
            help='نوع التقرير'
        )
        parser.add_argument(
            '--start_date',
            type=str,
            help='تاريخ البداية (YYYY-MM-DD)'
        )
        parser.add_argument(
            '--end_date',
            type=str,
            help='تاريخ النهاية (YYYY-MM-DD)'
        )
        parser.add_argument(
            '--account',
            type=str,
            help='رقم الحساب'
        )
        parser.add_argument(
            '--output',
            type=str,
            default='pdf',
            choices=['pdf', 'html'],
            help='نوع ملف المخرجات'
        )

    def handle(self, *args, **options):
        report_type = options['report_type']
        output_type = options['output']
        
        try:
            # تحليل التواريخ
            start_date = None
            end_date = None
            if options['start_date']:
                start_date = timezone.datetime.strptime(
                    options['start_date'], '%Y-%m-%d'
                ).date()
            if options['end_date']:
                end_date = timezone.datetime.strptime(
                    options['end_date'], '%Y-%m-%d'
                ).date()
            
            # إنشاء التقرير
            if report_type == 'daily_journal':
                self.generate_daily_journal(start_date or date.today(), output_type)
            elif report_type == 'account_statement':
                if not options['account']:
                    raise CommandError('يجب تحديد رقم الحساب')
                self.generate_account_statement(
                    options['account'],
                    start_date,
                    end_date,
                    output_type
                )
            elif report_type == 'salary_report':
                self.generate_salary_report(start_date, end_date, output_type)
            elif report_type == 'balance_sheet':
                self.generate_balance_sheet(end_date or date.today(), output_type)
            elif report_type == 'trial_balance':
                self.generate_trial_balance(end_date or date.today(), output_type)
            elif report_type == 'financial_position':
                self.generate_financial_position(
                    end_date or date.today(),
                    output_type
                )
            
        except Exception as e:
            raise CommandError(f'فشل إنشاء التقرير: {str(e)}')

    def generate_pdf(self, template_name, context, output_file):
        """توليد ملف PDF من قالب HTML"""
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(settings.MEDIA_ROOT, 'reports')
        os.makedirs(reports_dir, exist_ok=True)
        
        # إضافة معلومات المؤسسة للسياق
        context['organization'] = Organization.objects.first()
        context['generated_at'] = timezone.now()
        
        # توليد HTML
        html_string = render_to_string(template_name, context)
        
        # تحويل HTML إلى PDF
        html = weasyprint.HTML(string=html_string)
        pdf_file = os.path.join(reports_dir, output_file)
        html.write_pdf(pdf_file)
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء التقرير: {output_file}')
        )
        return pdf_file

    def generate_daily_journal(self, date, output_type):
        """توليد اليومية العامة"""
        transactions = FinancialTransaction.objects.filter(
            transaction_date=date
        ).order_by('created_at')
        
        context = {
            'date': date,
            'transactions': transactions,
            'total_debit': transactions.filter(
                transaction_type='payment'
            ).aggregate(Sum('amount'))['amount__sum'] or 0,
            'total_credit': transactions.filter(
                transaction_type__in=['receipt', 'journal']
            ).aggregate(Sum('amount'))['amount__sum'] or 0
        }
        
        if output_type == 'pdf':
            return self.generate_pdf(
                'reports/daily_journal.html',
                context,
                f'daily_journal_{date}.pdf'
            )
        else:
            return render_to_string('reports/daily_journal.html', context)

    def generate_account_statement(self, account_number, start_date, end_date, output_type):
        """توليد كشف حساب"""
        account = AccountGuide.objects.get(account_number=account_number)
        
        transactions = FinancialTransaction.objects.filter(
            account=account,
            transaction_date__range=[start_date, end_date]
        ).order_by('transaction_date', 'created_at')
        
        # حساب الرصيد المدور
        previous_balance = FinancialTransaction.objects.filter(
            account=account,
            transaction_date__lt=start_date
        ).aggregate(
            credits=Sum('amount', filter=models.Q(transaction_type__in=['receipt', 'journal'])),
            debits=Sum('amount', filter=models.Q(transaction_type='payment'))
        )
        
        opening_balance = (
            (previous_balance['credits'] or 0) -
            (previous_balance['debits'] or 0)
        )
        
        context = {
            'account': account,
            'start_date': start_date,
            'end_date': end_date,
            'opening_balance': opening_balance,
            'transactions': transactions,
            'total_debit': transactions.filter(
                transaction_type='payment'
            ).aggregate(Sum('amount'))['amount__sum'] or 0,
            'total_credit': transactions.filter(
                transaction_type__in=['receipt', 'journal']
            ).aggregate(Sum('amount'))['amount__sum'] or 0
        }
        
        if output_type == 'pdf':
            return self.generate_pdf(
                'reports/account_statement.html',
                context,
                f'account_statement_{account_number}_{start_date}_{end_date}.pdf'
            )
        else:
            return render_to_string('reports/account_statement.html', context)

    def generate_salary_report(self, start_date, end_date, output_type):
        """توليد تقرير الرواتب"""
        if not start_date:
            start_date = timezone.now().replace(day=1).date()
        if not end_date:
            end_date = timezone.now().date()
        
        salary_sheets = SalarySheet.objects.filter(
            month__range=[start_date.month, end_date.month],
            year__range=[start_date.year, end_date.year]
        ).select_related('employee')
        
        context = {
            'start_date': start_date,
            'end_date': end_date,
            'salary_sheets': salary_sheets,
            'total_base_salary': salary_sheets.aggregate(
                Sum('base_salary')
            )['base_salary__sum'] or 0,
            'total_allowances': salary_sheets.aggregate(
                Sum('total_allowances')
            )['total_allowances__sum'] or 0,
            'total_deductions': salary_sheets.aggregate(
                Sum('total_deductions')
            )['total_deductions__sum'] or 0,
            'total_net': salary_sheets.aggregate(
                Sum('net_salary')
            )['net_salary__sum'] or 0
        }
        
        if output_type == 'pdf':
            return self.generate_pdf(
                'reports/salary_report.html',
                context,
                f'salary_report_{start_date}_{end_date}.pdf'
            )
        else:
            return render_to_string('reports/salary_report.html', context)