# Generated by Django 4.2.7 on 2025-06-06 08:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("employees", "0001_initial"),
        ("users", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="employee",
            name="department",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="users.department",
                verbose_name="الدائرة",
            ),
        ),
        migrations.AddField(
            model_name="employee",
            name="division",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="employees.division",
                verbose_name="الشعبة",
            ),
        ),
        migrations.AddField(
            model_name="employee",
            name="job_title",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="employees.jobtitle",
                verbose_name="العنوان الوظيفي",
            ),
        ),
        migrations.AddField(
            model_name="employee",
            name="section",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="employees.section",
                verbose_name="القسم",
            ),
        ),
        migrations.AddField(
            model_name="employee",
            name="stage",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="employees.stage",
                verbose_name="المرحلة",
            ),
        ),
        migrations.AddField(
            model_name="division",
            name="section",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="employees.section",
                verbose_name="القسم المرتبط",
            ),
        ),
        migrations.AddField(
            model_name="deduction",
            name="employee",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                to="employees.employee",
                verbose_name="الموظف",
            ),
        ),
        migrations.AddField(
            model_name="basesalary",
            name="employee",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                to="employees.employee",
                verbose_name="الموظف",
            ),
        ),
        migrations.AddField(
            model_name="allowance",
            name="employee",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                to="employees.employee",
                verbose_name="الموظف",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="salarysheet",
            unique_together={("employee", "month", "year")},
        ),
    ]
