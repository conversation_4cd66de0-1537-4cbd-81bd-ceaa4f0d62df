from django.db import models
from django.utils.translation import gettext_lazy as _

class AccountGuide(models.Model):
    account_number = models.CharField(_('رقم الحساب'), max_length=50, unique=True)
    account_name = models.CharField(_('اسم الحساب'), max_length=200)
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('الحساب الرئيسي')
    )
    notes = models.TextField(_('الملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('دليل الحساب')
        verbose_name_plural = _('دليل الحسابات')
    
    def __str__(self):
        return f"{self.account_name} ({self.account_number})"

class Fund(models.Model):
    fund_number = models.CharField(_('رقم الصندوق'), max_length=50, unique=True)
    fund_name = models.Char<PERSON><PERSON>(_('اسم الصندوق'), max_length=200)
    notes = models.TextField(_('الملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('الصندوق')
        verbose_name_plural = _('الصناديق')
    
    def __str__(self):
        return self.fund_name

class Bank(models.Model):
    bank_number = models.CharField(_('رقم المصرف'), max_length=50, unique=True)
    bank_name = models.CharField(_('اسم المصرف'), max_length=200)
    notes = models.TextField(_('الملاحظات'), blank=True)
    
    class Meta:
        verbose_name = _('المصرف')
        verbose_name_plural = _('المصارف')
    
    def __str__(self):
        return self.bank_name

class BankBranch(models.Model):
    branch_number = models.CharField(_('رقم الفرع'), max_length=50, unique=True)
    branch_name = models.CharField(_('اسم الفرع'), max_length=200)
    bank = models.ForeignKey(
        Bank,
        on_delete=models.CASCADE,
        verbose_name=_('المصرف')
    )
    bank_account = models.ForeignKey(
        'BankAccount',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('الحساب البنكي')
    )
    
    class Meta:
        verbose_name = _('فرع المصرف')
        verbose_name_plural = _('فروع المصارف')
    
    def __str__(self):
        return f"{self.branch_name} - {self.bank.bank_name}"

class BankAccount(models.Model):
    account_number = models.CharField(_('رقم الحساب'), max_length=50, unique=True)
    account_name = models.CharField(_('اسم الحساب البنكي'), max_length=200)
    bank = models.ForeignKey(
        Bank,
        on_delete=models.CASCADE,
        verbose_name=_('المصرف')
    )
    currency = models.ForeignKey(
        'Currency',
        on_delete=models.PROTECT,
        verbose_name=_('العملة')
    )
    opening_balance = models.DecimalField(
        _('الرصيد الافتتاحي'),
        max_digits=15,
        decimal_places=3,
        default=0
    )
    current_balance = models.DecimalField(
        _('الرصيد الحالي'),
        max_digits=15,
        decimal_places=3,
        default=0
    )
    
    class Meta:
        verbose_name = _('الحساب البنكي')
        verbose_name_plural = _('الحسابات البنكية')
    
    def __str__(self):
        return f"{self.account_name} - {self.bank.bank_name}"

class FinancialTransaction(models.Model):
    class TransactionType(models.TextChoices):
        PAYMENT = 'payment', _('سند صرف')
        RECEIPT = 'receipt', _('سند قبض')
        JOURNAL = 'journal', _('قيد يومية')

    transaction_number = models.CharField(_('رقم المعاملة'), max_length=50, unique=True)
    transaction_date = models.DateField(_('تاريخ المعاملة'))
    transaction_type = models.CharField(
        _('نوع المعاملة'),
        max_length=10,
        choices=TransactionType.choices
    )
    amount = models.DecimalField(_('المبلغ'), max_digits=15, decimal_places=3)
    description = models.TextField(_('الوصف'))
    bank_account = models.ForeignKey(
        BankAccount,
        on_delete=models.PROTECT,
        verbose_name=_('الحساب البنكي')
    )
    account = models.ForeignKey(
        AccountGuide,
        on_delete=models.PROTECT,
        verbose_name=_('الحساب')
    )
    notes = models.TextField(_('الملاحظات'), blank=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('المعاملة المالية')
        verbose_name_plural = _('المعاملات المالية')
        ordering = ['-transaction_date', '-created_at']

    def __str__(self):
        return f"{self.get_transaction_type_display()} - {self.transaction_number}"