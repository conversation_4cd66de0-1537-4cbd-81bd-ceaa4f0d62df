#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل تطبيق سطح المكتب المحسن
يتضمن فحص المتطلبات وإعداد النظام تلقائياً
"""

import sys
import subprocess
from pathlib import Path

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           🏢 نظام إدارة الرواتب والمحاسبة                    ║
    ║                                                              ║
    ║                    تطبيق سطح المكتب                         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_requirements():
    """فحص المتطلبات"""
    print("📦 فحص المتطلبات...")

    required_packages = [
        ('django', 'Django'),
        ('PyQt5', 'PyQt5'),
        ('pandas', 'pandas'),
        ('psycopg2', 'psycopg2-binary')
    ]

    missing_packages = []

    for package_name, display_name in required_packages:
        try:
            __import__(package_name)
            print(f"✅ {display_name}")
        except ImportError:
            print(f"❌ {display_name} - غير مثبت")
            missing_packages.append(display_name)

    if missing_packages:
        print(f"\n⚠️  المتطلبات المفقودة: {', '.join(missing_packages)}")
        print("💡 لتثبيت المتطلبات:")
        print("   python install_requirements.py")
        print("   أو")
        print("   pip install -r requirements.txt")
        return False

    return True

def setup_django():
    """إعداد Django"""
    print("⚙️  إعداد Django...")
    
    try:
        # فحص وجود قاعدة البيانات
        if not Path('db.sqlite3').exists():
            print("📊 إنشاء قاعدة البيانات...")
            subprocess.run([sys.executable, 'manage.py', 'migrate'], 
                         check=True, capture_output=True)
            print("✅ تم إنشاء قاعدة البيانات")
        
        # فحص وجود مستخدم إداري
        result = subprocess.run([sys.executable, 'manage.py', 'shell', '-c', 
                               "from users.models import CustomUser; print(CustomUser.objects.filter(is_superuser=True).exists())"],
                              capture_output=True, text=True)
        
        if 'True' not in result.stdout:
            print("👤 إنشاء مستخدم إداري...")
            create_admin_user()
        else:
            print("✅ المستخدم الإداري موجود")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في إعداد Django: {e}")
        return False
    
    return True

def create_admin_user():
    """إنشاء مستخدم إداري"""
    script = """
from users.models import CustomUser
from django.contrib.auth.hashers import make_password

# حذف أي مستخدم admin موجود
CustomUser.objects.filter(username='admin').delete()

# إنشاء مستخدم جديد
user = CustomUser.objects.create(
    username='admin',
    email='<EMAIL>',
    account_name='المدير العام',
    password=make_password('admin123'),
    is_staff=True,
    is_superuser=True,
    is_active=True
)

print('تم إنشاء المستخدم الإداري بنجاح')
"""
    
    try:
        subprocess.run([sys.executable, 'manage.py', 'shell', '-c', script], 
                      check=True, capture_output=True)
        print("✅ تم إنشاء المستخدم الإداري")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")

def start_desktop_app():
    """تشغيل تطبيق سطح المكتب"""
    print("🖥️  تشغيل تطبيق سطح المكتب...")
    print("📍 استخدم البيانات التالية لتسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("\n🚀 بدء التشغيل...")
    
    try:
        subprocess.run([sys.executable, 'desktop_app.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق بواسطة المستخدم")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص إصدار Python
    if not check_python_version():
        input("\nاضغط Enter للخروج...")
        return
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n💡 لتثبيت المتطلبات:")
        print("   pip install -r requirements.txt")
        input("\nاضغط Enter للخروج...")
        return
    
    # إعداد Django
    if not setup_django():
        input("\nاضغط Enter للخروج...")
        return
    
    print("\n" + "="*60)
    print("✅ تم إعداد النظام بنجاح!")
    print("="*60)
    
    # تشغيل التطبيق
    start_desktop_app()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")
        print("يرجى التواصل مع الدعم الفني")
    finally:
        input("\nاضغط Enter للخروج...")
